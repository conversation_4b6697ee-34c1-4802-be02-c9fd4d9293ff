#!/bin/bash

# 简单打包脚本 - 基于当前运行方式

set -e

VERSION=$(date +%Y%m%d_%H%M%S)
PACKAGE_NAME="ota_server_simple_${VERSION}"

echo "开始打包OTA服务器..."
echo "版本: $VERSION"

# 创建打包目录
rm -rf dist
mkdir -p dist/$PACKAGE_NAME

# 复制核心文件
echo "复制核心文件..."
cp app.py dist/$PACKAGE_NAME/
cp start.py dist/$PACKAGE_NAME/
cp requirements.txt dist/$PACKAGE_NAME/
cp install.sh dist/$PACKAGE_NAME/
chmod +x dist/$PACKAGE_NAME/install.sh

# 复制模板和静态文件
echo "复制模板文件..."
cp -r templates dist/$PACKAGE_NAME/
mkdir -p dist/$PACKAGE_NAME/static/ota
mkdir -p dist/$PACKAGE_NAME/instance
mkdir -p dist/$PACKAGE_NAME/logs

# 复制文档
echo "复制文档..."
cp README.md dist/$PACKAGE_NAME/ 2>/dev/null || echo "# OTA Server" > dist/$PACKAGE_NAME/README.md
cp 使用说明.md dist/$PACKAGE_NAME/ 2>/dev/null || true

# 创建nginx配置示例
echo "创建配置示例..."
cat > dist/$PACKAGE_NAME/nginx.conf.example << 'EOF'
server {
    listen 80;
    server_name your-domain.com;
    
    client_max_body_size 1G;
    
    location /ota/ {
        alias /path/to/ota_server/static/ota/;
        expires 1d;
    }
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# 创建环境配置示例
cat > dist/$PACKAGE_NAME/.env.example << 'EOF'
SECRET_KEY=change-this-secret-key
FLASK_ENV=production
DATABASE_URL=sqlite:///instance/ota_config.db
UPLOAD_FOLDER=/path/to/ota_server/static/ota
DOWNLOAD_BASE_URL=http://your-domain.com:8000/ota/
PORT=8000
ADMIN_USERNAME=admin
ADMIN_PASSWORD=123456
LOG_LEVEL=INFO
LOG_FILE=/path/to/ota_server/logs/ota_server.log
EOF

# 创建README
cat > dist/$PACKAGE_NAME/INSTALL.md << 'EOF'
# OTA服务器安装说明

## 快速安装

1. 解压文件到目标目录
2. 运行安装脚本：`bash install.sh`
3. 启动服务：`./run.sh`

## 手动安装

1. 创建虚拟环境：`python3 -m venv venv`
2. 激活环境：`source venv/bin/activate`
3. 安装依赖：`pip install -r requirements.txt`
4. 复制配置：`cp .env.example .env`
5. 编辑配置：`nano .env`
6. 启动服务：`python start.py`

## 访问

- 管理界面：http://localhost:8000
- API接口：http://localhost:8000/api/update

## 支持的升级包类型

- full: 全量升级包
- incremental: 增量升级包  
- targetfiles: 目标文件包
EOF

# 创建版本信息
cat > dist/$PACKAGE_NAME/VERSION << EOF
OTA Server Simple Package
版本: $VERSION
打包时间: $(date)
运行方式: 直接运行，无需额外用户
Python要求: 3.8+
EOF

# 清理临时文件
echo "清理临时文件..."
find dist/$PACKAGE_NAME -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find dist/$PACKAGE_NAME -name "*.pyc" -delete 2>/dev/null || true

# 创建压缩包
echo "创建压缩包..."
cd dist
tar -czf ${PACKAGE_NAME}.tar.gz $PACKAGE_NAME
zip -r ${PACKAGE_NAME}.zip $PACKAGE_NAME >/dev/null 2>&1

# 显示结果
echo
echo "=================================="
echo "打包完成！"
echo "=================================="
echo "包名: $PACKAGE_NAME"
echo "文件:"
echo "  ${PACKAGE_NAME}.tar.gz ($(du -h ${PACKAGE_NAME}.tar.gz | cut -f1))"
echo "  ${PACKAGE_NAME}.zip ($(du -h ${PACKAGE_NAME}.zip | cut -f1))"
echo
echo "安装方法:"
echo "1. 解压: tar -xzf ${PACKAGE_NAME}.tar.gz"
echo "2. 进入: cd $PACKAGE_NAME"
echo "3. 安装: bash install.sh"
echo "4. 启动: ./run.sh"
echo "=================================="

# 显示包内容
echo "包含文件:"
ls -la $PACKAGE_NAME/
