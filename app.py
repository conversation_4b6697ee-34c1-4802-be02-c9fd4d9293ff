import os
import uuid
import hashlib
from datetime import datetime, timezone
from flask import Flask, request, jsonify, render_template, redirect, url_for, flash, send_from_directory
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, login_required, logout_user, current_user, UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename

# 配置管理
class Config:
    """应用配置类"""

    # 基础配置
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', os.path.join('static', 'ota'))
    ALLOWED_EXTENSIONS = {'bin', 'img', 'zip', 'tar', 'gz', 'bz2', 'xz', 'hex', 'elf', 'apk', 'deb', 'rpm'}

    # 文件大小限制 (默认500MB)
    MAX_FILE_SIZE = int(os.environ.get('MAX_FILE_SIZE', 500 * 1024 * 1024))

    # URL配置
    DOWNLOAD_BASE_URL = os.environ.get('DOWNLOAD_BASE_URL', 'http://ota.tyw.com/ota/')

    # 数据库配置
    DATABASE_URI = os.environ.get('DATABASE_URL', 'sqlite:///ota_config.db')

    # 安全配置
    SECRET_KEY = os.environ.get('SECRET_KEY', 'change-this-secret-key')

    # 管理员配置
    ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME', 'admin')
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD', '123456')

    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.environ.get('LOG_FILE', 'ota_server.log')

    # 性能配置
    MAX_CONTENT_LENGTH = int(os.environ.get('MAX_CONTENT_LENGTH', 1024 * 1024 * 1024))  # 1GB

    @classmethod
    def validate(cls):
        """验证配置"""
        errors = []

        # 验证必要的配置
        if cls.SECRET_KEY == 'change-this-secret-key':
            errors.append("SECRET_KEY should be changed from default value")

        if len(cls.SECRET_KEY) < 16:
            errors.append("SECRET_KEY should be at least 16 characters long")

        if cls.MAX_FILE_SIZE <= 0:
            errors.append("MAX_FILE_SIZE must be positive")

        if not cls.DOWNLOAD_BASE_URL.startswith(('http://', 'https://')):
            errors.append("DOWNLOAD_BASE_URL must start with http:// or https://")

        if not cls.DOWNLOAD_BASE_URL.endswith('/'):
            cls.DOWNLOAD_BASE_URL += '/'

        return errors

# 使用配置类
config = Config()
UPLOAD_FOLDER = config.UPLOAD_FOLDER
ALLOWED_EXTENSIONS = config.ALLOWED_EXTENSIONS
MAX_FILE_SIZE = config.MAX_FILE_SIZE
DOWNLOAD_BASE_URL = config.DOWNLOAD_BASE_URL
DATABASE_URI = config.DATABASE_URI
SECRET_KEY = config.SECRET_KEY

# 配置验证
config_errors = config.validate()
if config_errors:
    print("Configuration errors found:")
    for error in config_errors:
        print(f"  - {error}")
    if any("SECRET_KEY" in error for error in config_errors):
        print("WARNING: Using default SECRET_KEY is not secure for production!")

# Flask应用
app = Flask(__name__)
app.config.update({
    'UPLOAD_FOLDER': UPLOAD_FOLDER,
    'SQLALCHEMY_DATABASE_URI': DATABASE_URI,
    'SQLALCHEMY_TRACK_MODIFICATIONS': False,
    'SECRET_KEY': SECRET_KEY,
    'MAX_CONTENT_LENGTH': config.MAX_CONTENT_LENGTH,
})

# 日志配置
import logging
from logging.handlers import RotatingFileHandler

def setup_logging():
    """设置日志配置"""
    if not app.debug:
        # 设置日志级别
        log_level = getattr(logging, config.LOG_LEVEL.upper(), logging.INFO)

        # 文件日志处理器
        file_handler = RotatingFileHandler(
            config.LOG_FILE,
            maxBytes=10240000,  # 10MB
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(log_level)
        app.logger.addHandler(file_handler)

        # 控制台日志处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s'
        ))
        console_handler.setLevel(log_level)
        app.logger.addHandler(console_handler)

        app.logger.setLevel(log_level)
        app.logger.info('OTA Server startup')

# 初始化日志
setup_logging()

# 全局错误处理器
@app.errorhandler(404)
def not_found_error(error):
    if request.path.startswith('/api/'):
        return jsonify({'error': 'not_found', 'message': 'Resource not found'}), 404
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    app.logger.error(f'Server Error: {error}')
    if request.path.startswith('/api/'):
        return jsonify({'error': 'internal_error', 'message': 'Internal server error'}), 500
    return render_template('500.html'), 500

@app.errorhandler(413)
def too_large(error):
    if request.path.startswith('/api/'):
        return jsonify({'error': 'file_too_large', 'message': 'File too large'}), 413
    flash('文件过大', 'danger')
    return redirect(request.referrer or url_for('admin_list'))

@app.before_request
def before_request():
    """请求前处理"""
    # 确保上传目录存在
    if not os.path.exists(UPLOAD_FOLDER):
        try:
            os.makedirs(UPLOAD_FOLDER, exist_ok=True)
        except OSError as e:
            app.logger.error(f"Failed to create upload folder: {e}")

@app.after_request
def after_request(response):
    """请求后处理"""
    # 添加安全头
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    return response

# 初始化数据库
db = SQLAlchemy(app)

# 数据库模型
class User(UserMixin, db.Model):
    __tablename__ = 'users'
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    is_admin = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Device(db.Model):
    __tablename__ = 'devices'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, nullable=False, index=True)
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True, nullable=False, index=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), nullable=False)
    last_check_time = db.Column(db.DateTime, index=True)

    # 关联版本升级
    upgrades = db.relationship('DeviceUpgrade', backref='device', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Device {self.name}>'

class DeviceUpgrade(db.Model):
    __tablename__ = 'device_upgrades'
    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.Integer, db.ForeignKey('devices.id'), nullable=False, index=True)
    from_version = db.Column(db.String(64), nullable=False, index=True)
    to_version = db.Column(db.String(64), nullable=False, index=True)
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True, nullable=False, index=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), nullable=False)
    download_count = db.Column(db.Integer, default=0, nullable=False)

    # 关联升级包
    packages = db.relationship('UpgradePackage', backref='upgrade', lazy='dynamic', cascade='all, delete-orphan')

    # 复合索引
    __table_args__ = (
        db.Index('idx_device_from_version', 'device_id', 'from_version'),
        db.Index('idx_device_active', 'device_id', 'is_active'),
    )

    def get_storage_path(self):
        """获取存储路径"""
        return os.path.join(UPLOAD_FOLDER, self.device.name, f"{self.from_version}_to_{self.to_version}")

    def __repr__(self):
        return f'<DeviceUpgrade {self.from_version} -> {self.to_version}>'

class UpgradePackage(db.Model):
    __tablename__ = 'upgrade_packages'
    id = db.Column(db.Integer, primary_key=True)
    upgrade_id = db.Column(db.Integer, db.ForeignKey('device_upgrades.id'), nullable=False, index=True)
    package_type = db.Column(db.String(32), nullable=False, index=True)  # full, incremental, patch等
    filename = db.Column(db.String(256), nullable=False, index=True)
    original_filename = db.Column(db.String(256), nullable=False)
    file_size = db.Column(db.BigInteger)
    file_md5 = db.Column(db.String(32))
    file_sha256 = db.Column(db.String(64))
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True, nullable=False, index=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    download_count = db.Column(db.Integer, default=0, nullable=False)

    # 复合索引
    __table_args__ = (
        db.Index('idx_upgrade_type_active', 'upgrade_id', 'package_type', 'is_active'),
        db.Index('idx_upgrade_filename', 'upgrade_id', 'filename'),
    )

    def get_file_path(self):
        """获取文件完整路径"""
        return os.path.join(self.upgrade.get_storage_path(), self.filename)

    def get_download_url(self):
        """获取下载URL"""
        return f"{DOWNLOAD_BASE_URL}{self.upgrade.device.name}/{self.upgrade.from_version}_to_{self.upgrade.to_version}/{self.filename}"

    def __repr__(self):
        return f'<UpgradePackage {self.package_type}: {self.filename}>'

class DownloadLog(db.Model):
    __tablename__ = 'download_logs'
    id = db.Column(db.Integer, primary_key=True)
    package_id = db.Column(db.Integer, db.ForeignKey('upgrade_packages.id'), nullable=False)
    client_ip = db.Column(db.String(45))
    user_agent = db.Column(db.String(256))
    download_time = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)

    package = db.relationship('UpgradePackage', backref='download_logs')

# Flask-Login 配置
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = '请先登录'
login_manager.login_message_category = 'warning'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# 工具函数
def allowed_file(filename):
    """检查文件扩展名是否允许"""
    if not filename or '.' not in filename:
        return False

    ext = filename.rsplit('.', 1)[1].lower()
    return ext in ALLOWED_EXTENSIONS

def validate_filename(filename):
    """验证文件名安全性"""
    if not filename:
        return False

    # 检查文件名长度
    if len(filename) > 255:
        return False

    # 检查危险字符
    dangerous_chars = ['..', '/', '\\', ':', '*', '?', '"', '<', '>', '|', '\0']
    for char in dangerous_chars:
        if char in filename:
            return False

    # 检查是否为隐藏文件
    if filename.startswith('.'):
        return False

    return True

def validate_file_size(file_path):
    """验证文件大小"""
    try:
        size = os.path.getsize(file_path)
        return size <= MAX_FILE_SIZE
    except OSError:
        return False

def validate_device_name(name):
    """验证设备名称"""
    if not name or not isinstance(name, str):
        return False

    name = name.strip()
    if not name or len(name) > 64:
        return False

    # 只允许字母、数字、下划线、连字符
    import re
    if not re.match(r'^[a-zA-Z0-9_-]+$', name):
        return False

    return True

def validate_version(version):
    """验证版本号"""
    if not version or not isinstance(version, str):
        return False

    version = version.strip()
    if not version or len(version) > 64:
        return False

    # 允许版本号包含字母、数字、点、连字符
    import re
    if not re.match(r'^[a-zA-Z0-9._-]+$', version):
        return False

    return True

def validate_package_type(package_type):
    """验证升级包类型"""
    if not package_type or not isinstance(package_type, str):
        return False

    package_type = package_type.strip().lower()
    allowed_types = {'full', 'incremental', 'patch', 'delta', 'bootloader', 'kernel', 'rootfs'}

    return package_type in allowed_types

def sanitize_input(text, max_length=None):
    """清理输入文本"""
    if not text:
        return ''

    text = str(text).strip()

    if max_length and len(text) > max_length:
        text = text[:max_length]

    # 移除潜在的危险字符
    import re
    text = re.sub(r'[<>"\']', '', text)

    return text

def generate_unique_filename(original_filename, prefix=None):
    """生成安全的唯一文件名"""
    if not original_filename or not validate_filename(original_filename):
        return None

    ext = ''
    if '.' in original_filename:
        ext = '.' + original_filename.rsplit('.', 1)[1].lower()

    unique_id = uuid.uuid4().hex[:8]
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    if prefix:
        safe_prefix = secure_filename(prefix)[:20]  # 限制前缀长度
        filename = f"{safe_prefix}_{timestamp}_{unique_id}{ext}"
    else:
        filename = f"firmware_{timestamp}_{unique_id}{ext}"

    return filename

def create_storage_directory(device_name, from_version, to_version):
    """创建存储目录"""
    # 清理路径组件，防止路径遍历攻击
    safe_device_name = secure_filename(device_name)
    safe_from_version = secure_filename(from_version)
    safe_to_version = secure_filename(to_version)

    if not safe_device_name or not safe_from_version or not safe_to_version:
        raise ValueError("Invalid path components")

    storage_path = os.path.join(UPLOAD_FOLDER, safe_device_name, f"{safe_from_version}_to_{safe_to_version}")

    # 确保路径在允许的目录内
    abs_upload_folder = os.path.abspath(UPLOAD_FOLDER)
    abs_storage_path = os.path.abspath(storage_path)

    if not abs_storage_path.startswith(abs_upload_folder):
        raise ValueError("Path traversal attempt detected")

    os.makedirs(storage_path, exist_ok=True)
    return storage_path

def calculate_file_md5(file_path):
    """计算文件MD5值"""
    if not os.path.exists(file_path):
        return None

    try:
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except (OSError, IOError) as e:
        app.logger.error(f"Error calculating MD5 for {file_path}: {e}")
        return None

def calculate_file_sha256(file_path):
    """计算文件SHA256值"""
    if not os.path.exists(file_path):
        return None

    try:
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    except (OSError, IOError) as e:
        app.logger.error(f"Error calculating SHA256 for {file_path}: {e}")
        return None

def get_client_ip():
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr

def version_compare(version1, version2):
    """版本号比较
    返回值:
    -1: version1 < version2 (需要更新)
     0: version1 == version2 (相同版本)
     1: version1 > version2 (当前版本更新)
    """
    def normalize_version(v):
        """标准化版本号"""
        if not v:
            return [0]

        import re
        # 处理常见的版本号格式：v1.2.3, 1.2.3-beta, 1.2.3_rc1等
        v_str = str(v).lower()

        # 移除常见前缀
        v_str = re.sub(r'^v', '', v_str)

        # 分离主版本号和后缀
        main_version = re.match(r'^(\d+(?:\.\d+)*)', v_str)
        if not main_version:
            # 如果没有找到数字版本，返回原字符串的hash值作为比较基准
            return [hash(v_str) % 1000000]

        # 解析主版本号
        parts = []
        for part in main_version.group(1).split('.'):
            if part.isdigit():
                parts.append(int(part))

        # 处理后缀（alpha < beta < rc < release）
        suffix_order = {'alpha': -3, 'beta': -2, 'rc': -1, 'release': 0}
        suffix_value = 0

        for suffix, value in suffix_order.items():
            if suffix in v_str:
                suffix_value = value
                break

        # 如果没有后缀，认为是正式版本
        if suffix_value == 0 and not any(s in v_str for s in suffix_order.keys()):
            suffix_value = 1

        parts.append(suffix_value)
        return parts if parts else [0]

    try:
        # 边界条件检查
        if version1 is None and version2 is None:
            return 0
        if version1 is None:
            return -1
        if version2 is None:
            return 1
        if version1 == version2:
            return 0

        v1_parts = normalize_version(version1)
        v2_parts = normalize_version(version2)

        # 补齐长度
        max_len = max(len(v1_parts), len(v2_parts))
        v1_parts.extend([0] * (max_len - len(v1_parts)))
        v2_parts.extend([0] * (max_len - len(v2_parts)))

        # 逐位比较
        for i in range(max_len):
            if v1_parts[i] < v2_parts[i]:
                return -1
            elif v1_parts[i] > v2_parts[i]:
                return 1

        return 0
    except Exception as e:
        app.logger.warning(f"Version comparison failed for {version1} vs {version2}: {e}")
        # 如果无法解析版本号，则按字符串比较
        try:
            if str(version1) < str(version2):
                return -1
            elif str(version1) > str(version2):
                return 1
            else:
                return 0
        except Exception:
            return 0

def log_download(package, client_ip, user_agent):
    """记录下载日志"""
    try:
        log = DownloadLog(
            package_id=package.id,
            client_ip=client_ip[:45] if client_ip else None,  # 限制IP长度
            user_agent=user_agent[:256] if user_agent else None  # 限制User-Agent长度
        )
        db.session.add(log)
        package.download_count += 1
        package.upgrade.download_count += 1
        package.upgrade.device.last_check_time = datetime.now(timezone.utc)
        db.session.commit()
    except Exception as e:
        app.logger.error(f"Error logging download: {e}")
        db.session.rollback()
        # 即使日志记录失败，也不应该影响下载功能

def find_upgrade_path(device, current_version):
    """查找升级路径"""
    # 使用索引优化的查询
    upgrade = DeviceUpgrade.query.filter(
        DeviceUpgrade.device_id == device.id,
        DeviceUpgrade.from_version == current_version,
        DeviceUpgrade.is_active == True
    ).first()

    return upgrade

def get_device_by_name(device_name):
    """根据名称获取设备（带缓存优化）"""
    return Device.query.filter(
        Device.name == device_name,
        Device.is_active == True
    ).first()

def get_upgrade_package(upgrade_id, package_type):
    """获取升级包（优化查询）"""
    if not upgrade_id or not package_type:
        return None

    return UpgradePackage.query.filter(
        UpgradePackage.upgrade_id == upgrade_id,
        UpgradePackage.package_type == package_type,
        UpgradePackage.is_active == True
    ).first()

def safe_int(value, default=0):
    """安全的整数转换"""
    try:
        return int(value) if value is not None else default
    except (ValueError, TypeError):
        return default

def safe_str(value, default='', max_length=None):
    """安全的字符串转换"""
    try:
        result = str(value) if value is not None else default
        if max_length and len(result) > max_length:
            result = result[:max_length]
        return result
    except Exception:
        return default

def is_valid_device_upgrade_path(device_id, from_version, to_version):
    """验证升级路径的有效性"""
    if not device_id or not from_version or not to_version:
        return False, "Missing required parameters"

    if from_version == to_version:
        return False, "Source and target versions cannot be the same"

    # 检查设备是否存在
    device = Device.query.get(device_id)
    if not device:
        return False, "Device not found"

    if not device.is_active:
        return False, "Device is not active"

    # 检查是否已存在相同的升级路径
    existing = DeviceUpgrade.query.filter_by(
        device_id=device_id,
        from_version=from_version,
        to_version=to_version
    ).first()

    if existing:
        return False, "Upgrade path already exists"

    return True, "Valid upgrade path"

# 数据库初始化
def init_db():
    """初始化数据库"""
    try:
        with app.app_context():
            # 删除所有表并重新创建（因为结构变化较大）
            db.drop_all()
            db.create_all()

            admin_username = config.ADMIN_USERNAME
            admin_password = config.ADMIN_PASSWORD

            # 检查是否已存在管理员账号
            existing_admin = User.query.filter_by(username=admin_username).first()
            if not existing_admin:
                # 创建默认管理员账号
                admin = User(username=admin_username, is_admin=True)
                admin.set_password(admin_password)
                db.session.add(admin)
                db.session.commit()
                print(f'Created admin user: {admin_username}')
            else:
                print(f'Admin user already exists: {admin_username}')

            # 检查是否已存在示例设备
            existing_device = Device.query.filter_by(name='example_device').first()
            if not existing_device:
                # 创建示例设备和升级路径
                device = Device(
                    name='example_device',
                    description='示例设备',
                    is_active=True
                )
                db.session.add(device)
                db.session.commit()
                print('Created example device')

            print('Database initialized successfully')

    except Exception as e:
        print(f'Error initializing database: {e}')
        raise

# ============ 路由 ============

@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('admin_list'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('admin_list'))

    if request.method == 'POST':
        try:
            username = sanitize_input(request.form.get('username', ''), 64)
            password = request.form.get('password', '')

            # 基本验证
            if not username or not password:
                flash('用户名和密码不能为空', 'danger')
                return render_template('login.html')

            if len(username) > 64 or len(password) > 128:
                flash('用户名或密码长度超出限制', 'danger')
                return render_template('login.html')

            user = User.query.filter_by(username=username).first()

            if user and user.check_password(password):
                login_user(user)
                flash('登录成功', 'success')

                # 验证next参数，防止开放重定向
                next_page = request.args.get('next')
                if next_page and next_page.startswith('/'):
                    return redirect(next_page)
                else:
                    return redirect(url_for('admin_list'))
            else:
                flash('用户名或密码错误', 'danger')

        except Exception as e:
            app.logger.error(f"Login error: {e}")
            flash('登录过程中发生错误', 'danger')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('已登出', 'info')
    return redirect(url_for('login'))

# ============ OTA API 接口 ============

@app.route('/api/update', methods=['GET', 'POST'])
def api_update():
    """OTA更新检查接口"""
    try:
        device_name = request.values.get('device', '').strip()
        current_version = request.values.get('firmware', '').strip() or request.values.get('version', '').strip()
        package_type = request.values.get('type', 'full').strip()

        # 参数验证
        if not device_name or not current_version:
            return jsonify({
                'update': False,
                'error': 'missing_parameters',
                'message': 'Device name and firmware version are required'
            }), 400

        # 验证参数长度和格式
        if len(device_name) > 64 or len(current_version) > 64 or len(package_type) > 32:
            return jsonify({
                'update': False,
                'error': 'invalid_parameters',
                'message': 'Parameter length exceeds limit'
            }), 400

        # 查找设备
        device = get_device_by_name(device_name)
        if not device:
            return jsonify({
                'update': False,
                'error': 'unknown_device',
                'message': f'Device "{device_name}" not found or inactive'
            }), 404

        # 查找升级路径
        upgrade = find_upgrade_path(device, current_version)
        if not upgrade:
            # 更新检查时间（即使没有升级）
            try:
                device.last_check_time = datetime.now(timezone.utc)
                db.session.commit()
            except Exception as e:
                app.logger.error(f"Error updating last_check_time: {e}")
                db.session.rollback()

            return jsonify({
                'update': False,
                'error': 'no_upgrade_path',
                'message': 'No upgrade path available for this version',
                'current_version': current_version
            })

        # 查找对应类型的升级包
        package = get_upgrade_package(upgrade.id, package_type)

        if not package:
            # 如果没有指定类型的包，尝试查找full包
            package = get_upgrade_package(upgrade.id, 'full')

        if not package:
            db.session.commit()
            return jsonify({
                'update': False,
                'error': 'no_package',
                'message': f'No upgrade package available for type "{package_type}"',
                'current_version': current_version
            })

        # 验证文件是否存在
        file_path = package.get_file_path()
        if not os.path.exists(file_path):
            app.logger.error(f"Package file not found: {file_path}")
            return jsonify({
                'update': False,
                'error': 'file_not_found',
                'message': 'Upgrade package file not found'
            }), 500

        # 记录下载日志
        log_download(package, get_client_ip(), request.headers.get('User-Agent', ''))

        return jsonify({
            'update': True,
            'from_version': upgrade.from_version,
            'to_version': upgrade.to_version,
            'package_type': package.package_type,
            'url': package.get_download_url(),
            'file_size': package.file_size,
            'md5': package.file_md5,
            'sha256': package.file_sha256,
            'description': upgrade.description or package.description,
            'timestamp': datetime.now(timezone.utc).isoformat()
        })

    except Exception as e:
        app.logger.error(f"Error in api_update: {e}")
        db.session.rollback()
        return jsonify({
            'update': False,
            'error': 'internal_error',
            'message': 'Internal server error'
        }), 500

# 兼容旧版本API
@app.route('/update', methods=['GET', 'POST'])
def update():
    """兼容旧版本的更新接口"""
    return api_update()

# API信息接口
@app.route('/api/info', methods=['GET'])
def api_info():
    """API信息接口"""
    return jsonify({
        'api_version': '1.0',
        'server_name': 'OTA Server',
        'supported_methods': ['GET', 'POST'],
        'supported_parameters': {
            'device': 'Device name (required)',
            'firmware': 'Current firmware version (required)',
            'version': 'Alias for firmware parameter',
            'type': 'Package type (optional, default: full)'
        },
        'supported_package_types': ['full', 'incremental', 'patch', 'delta', 'bootloader', 'kernel', 'rootfs'],
        'timestamp': datetime.now(timezone.utc).isoformat()
    })

# 设备状态接口
@app.route('/api/device/<device_name>/status', methods=['GET'])
def api_device_status(device_name):
    """设备状态查询接口"""
    try:
        device = get_device_by_name(device_name)
        if not device:
            return jsonify({
                'error': 'device_not_found',
                'message': f'Device "{device_name}" not found'
            }), 404

        # 获取升级路径统计
        upgrades = DeviceUpgrade.query.filter_by(device_id=device.id, is_active=True).all()
        upgrade_paths = []

        for upgrade in upgrades:
            packages = UpgradePackage.query.filter_by(upgrade_id=upgrade.id, is_active=True).all()
            upgrade_paths.append({
                'from_version': upgrade.from_version,
                'to_version': upgrade.to_version,
                'description': upgrade.description,
                'download_count': upgrade.download_count,
                'package_count': len(packages),
                'package_types': [p.package_type for p in packages]
            })

        return jsonify({
            'device_name': device.name,
            'description': device.description,
            'is_active': device.is_active,
            'last_check_time': device.last_check_time.isoformat() if device.last_check_time else None,
            'created_at': device.created_at.isoformat(),
            'upgrade_paths': upgrade_paths,
            'total_upgrade_paths': len(upgrade_paths)
        })

    except Exception as e:
        app.logger.error(f"Error in api_device_status: {e}")
        return jsonify({
            'error': 'internal_error',
            'message': 'Internal server error'
        }), 500

# 健康检查接口
@app.route('/api/health', methods=['GET'])
def api_health():
    """健康检查接口"""
    try:
        # 检查数据库连接
        db.session.execute('SELECT 1')

        # 检查上传目录
        upload_dir_exists = os.path.exists(UPLOAD_FOLDER)
        upload_dir_writable = os.access(UPLOAD_FOLDER, os.W_OK) if upload_dir_exists else False

        return jsonify({
            'status': 'healthy',
            'database': 'connected',
            'upload_directory': {
                'exists': upload_dir_exists,
                'writable': upload_dir_writable
            },
            'timestamp': datetime.now(timezone.utc).isoformat()
        })

    except Exception as e:
        app.logger.error(f"Health check failed: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }), 500

# 统计接口
@app.route('/api/stats', methods=['GET'])
def api_stats():
    """统计信息接口"""
    try:
        # 设备统计
        total_devices = Device.query.count()
        active_devices = Device.query.filter_by(is_active=True).count()

        # 升级路径统计
        total_upgrades = DeviceUpgrade.query.count()
        active_upgrades = DeviceUpgrade.query.filter_by(is_active=True).count()

        # 升级包统计
        total_packages = UpgradePackage.query.count()
        active_packages = UpgradePackage.query.filter_by(is_active=True).count()

        # 存储统计
        total_size = db.session.query(db.func.sum(UpgradePackage.file_size)).filter_by(is_active=True).scalar() or 0

        # 下载统计
        total_downloads = db.session.query(db.func.sum(UpgradePackage.download_count)).scalar() or 0

        return jsonify({
            'devices': {
                'total': total_devices,
                'active': active_devices
            },
            'upgrade_paths': {
                'total': total_upgrades,
                'active': active_upgrades
            },
            'packages': {
                'total': total_packages,
                'active': active_packages
            },
            'storage': {
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / 1024 / 1024, 2) if total_size > 0 else 0
            },
            'downloads': {
                'total_count': total_downloads
            },
            'timestamp': datetime.now(timezone.utc).isoformat()
        })

    except Exception as e:
        app.logger.error(f"Error in api_stats: {e}")
        return jsonify({
            'error': 'internal_error',
            'message': 'Internal server error'
        }), 500

# ============ 管理界面路由 ============

@app.route('/admin')
@login_required
def admin_list():
    """设备列表页面"""
    devices = Device.query.order_by(Device.updated_at.desc()).all()

    # 计算存储统计（优化查询）
    try:
        # 使用数据库聚合查询提高效率
        total_size = db.session.query(db.func.sum(UpgradePackage.file_size)).filter_by(is_active=True).scalar() or 0
        file_count = UpgradePackage.query.filter_by(is_active=True).count()
        upgrade_count = DeviceUpgrade.query.filter_by(is_active=True).count()

        storage_stats = {
            'total_size': safe_int(total_size),
            'total_size_formatted': f"{total_size / 1024 / 1024:.1f} MB" if total_size > 0 else "0 MB",
            'file_count': file_count,
            'upgrade_count': upgrade_count
        }
    except Exception as e:
        app.logger.error(f"Error calculating storage stats: {e}")
        storage_stats = {
            'total_size': 0,
            'total_size_formatted': "0 MB",
            'file_count': 0,
            'upgrade_count': 0
        }

    return render_template('device_list.html',
                         devices=devices,
                         storage_stats=storage_stats)

@app.route('/admin/device/new', methods=['GET', 'POST'])
@login_required
def admin_device_new():
    """新增设备"""
    if request.method == 'POST':
        try:
            name = sanitize_input(request.form.get('name', ''), 64)
            description = sanitize_input(request.form.get('description', ''), 500)

            # 验证设备名称
            if not validate_device_name(name):
                flash('设备名称格式无效，只能包含字母、数字、下划线和连字符，长度不超过64字符', 'danger')
                return render_template('device_form.html', name=name, description=description)

            # 检查名称唯一性
            if Device.query.filter_by(name=name).first():
                flash('设备名称已存在', 'danger')
                return render_template('device_form.html', name=name, description=description)

            # 创建设备记录
            device = Device(
                name=name,
                description=description,
                is_active=True
            )

            db.session.add(device)
            db.session.commit()

            flash(f'设备 "{device.name}" 创建成功', 'success')
            return redirect(url_for('admin_device_detail', device_id=device.id))

        except Exception as e:
            app.logger.error(f"Error creating device: {e}")
            db.session.rollback()
            flash('创建设备时发生错误', 'danger')

    return render_template('device_form.html', title='新增设备')

@app.route('/admin/device/<int:device_id>')
@login_required
def admin_device_detail(device_id):
    """设备详情页面"""
    device = Device.query.get_or_404(device_id)
    upgrades = device.upgrades.order_by(DeviceUpgrade.created_at.desc()).all()

    return render_template('device_detail.html', device=device, upgrades=upgrades)

@app.route('/admin/device/<int:device_id>/edit', methods=['GET', 'POST'])
@login_required
def admin_device_edit(device_id):
    """编辑设备"""
    device = Device.query.get_or_404(device_id)

    if request.method == 'POST':
        name = request.form['name'].strip()
        description = request.form.get('description', '').strip()
        is_active = 'is_active' in request.form

        if not name:
            flash('设备名称不能为空', 'danger')
            return render_template('device_form.html', device=device)

        # 检查名称唯一性（排除自己）
        existing = Device.query.filter_by(name=name).first()
        if existing and existing.id != device.id:
            flash('设备名称已存在', 'danger')
            return render_template('device_form.html', device=device)

        # 更新基本信息
        device.name = name
        device.description = description
        device.is_active = is_active

        db.session.commit()
        flash(f'设备 "{device.name}" 更新成功', 'success')
        return redirect(url_for('admin_device_detail', device_id=device.id))

    return render_template('device_form.html', device=device, title='编辑设备')

@app.route('/admin/device/<int:device_id>/upgrade/new', methods=['GET', 'POST'])
@login_required
def admin_upgrade_new(device_id):
    """新增升级路径"""
    device = Device.query.get_or_404(device_id)

    if request.method == 'POST':
        try:
            from_version = sanitize_input(request.form.get('from_version', ''), 64)
            to_version = sanitize_input(request.form.get('to_version', ''), 64)
            description = sanitize_input(request.form.get('description', ''), 500)

            # 验证版本号
            if not validate_version(from_version) or not validate_version(to_version):
                flash('版本号格式无效，只能包含字母、数字、点、下划线和连字符', 'danger')
                return render_template('upgrade_form.html', device=device,
                                     from_version=from_version, to_version=to_version, description=description)

            # 检查版本号不能相同
            if from_version == to_version:
                flash('起始版本和目标版本不能相同', 'danger')
                return render_template('upgrade_form.html', device=device,
                                     from_version=from_version, to_version=to_version, description=description)

            # 检查升级路径是否已存在
            existing = DeviceUpgrade.query.filter_by(
                device_id=device.id,
                from_version=from_version,
                to_version=to_version
            ).first()

            if existing:
                flash('该升级路径已存在', 'danger')
                return render_template('upgrade_form.html', device=device,
                                     from_version=from_version, to_version=to_version, description=description)

            # 创建升级记录
            upgrade = DeviceUpgrade(
                device_id=device.id,
                from_version=from_version,
                to_version=to_version,
                description=description,
                is_active=True
            )

            db.session.add(upgrade)
            db.session.commit()

            flash(f'升级路径 "{from_version} → {to_version}" 创建成功', 'success')
            return redirect(url_for('admin_upgrade_detail', upgrade_id=upgrade.id))

        except Exception as e:
            app.logger.error(f"Error creating upgrade path: {e}")
            db.session.rollback()
            flash('创建升级路径时发生错误', 'danger')

    return render_template('upgrade_form.html', device=device, title='新增升级路径')

@app.route('/admin/upgrade/<int:upgrade_id>')
@login_required
def admin_upgrade_detail(upgrade_id):
    """升级详情页面"""
    upgrade = DeviceUpgrade.query.get_or_404(upgrade_id)
    packages = upgrade.packages.order_by(UpgradePackage.created_at.desc()).all()

    return render_template('upgrade_detail.html', upgrade=upgrade, packages=packages)

@app.route('/admin/upgrade/<int:upgrade_id>/package/new', methods=['GET', 'POST'])
@login_required
def admin_package_new(upgrade_id):
    """新增升级包"""
    upgrade = DeviceUpgrade.query.get_or_404(upgrade_id)

    if request.method == 'POST':
        try:
            package_type = sanitize_input(request.form.get('package_type', ''), 32)
            description = sanitize_input(request.form.get('description', ''), 500)
            firmware_file = request.files.get('firmware')

            # 验证升级包类型
            if not validate_package_type(package_type):
                flash('升级包类型无效', 'danger')
                return render_template('package_form.html', upgrade=upgrade,
                                     package_type=package_type, description=description)

            # 验证文件
            if not firmware_file or not firmware_file.filename:
                flash('请选择固件文件', 'danger')
                return render_template('package_form.html', upgrade=upgrade,
                                     package_type=package_type, description=description)

            # 验证文件名
            if not validate_filename(firmware_file.filename):
                flash('文件名包含非法字符', 'danger')
                return render_template('package_form.html', upgrade=upgrade,
                                     package_type=package_type, description=description)

            # 验证文件类型
            if not allowed_file(firmware_file.filename):
                flash('不支持的文件类型', 'danger')
                return render_template('package_form.html', upgrade=upgrade,
                                     package_type=package_type, description=description)

            # 检查是否已存在相同类型的升级包
            existing_package = UpgradePackage.query.filter_by(
                upgrade_id=upgrade.id,
                package_type=package_type,
                is_active=True
            ).first()

            if existing_package:
                flash(f'该升级路径已存在 "{package_type}" 类型的升级包', 'warning')

            # 创建存储目录
            storage_path = create_storage_directory(
                upgrade.device.name,
                upgrade.from_version,
                upgrade.to_version
            )

            # 生成文件名
            filename = generate_unique_filename(firmware_file.filename, package_type)
            if not filename:
                flash('生成文件名失败', 'danger')
                return render_template('package_form.html', upgrade=upgrade,
                                     package_type=package_type, description=description)

            file_path = os.path.join(storage_path, filename)

            # 保存文件
            firmware_file.save(file_path)

            # 验证文件大小
            if not validate_file_size(file_path):
                os.remove(file_path)  # 删除过大的文件
                flash(f'文件大小超过限制 ({MAX_FILE_SIZE // 1024 // 1024} MB)', 'danger')
                return render_template('package_form.html', upgrade=upgrade,
                                     package_type=package_type, description=description)

            # 计算文件信息
            file_size = os.path.getsize(file_path)
            file_md5 = calculate_file_md5(file_path)
            file_sha256 = calculate_file_sha256(file_path)

            # 创建升级包记录
            package = UpgradePackage(
                upgrade_id=upgrade.id,
                package_type=package_type,
                filename=filename,
                original_filename=firmware_file.filename[:256],  # 限制长度
                file_size=file_size,
                file_md5=file_md5,
                file_sha256=file_sha256,
                description=description,
                is_active=True
            )

            db.session.add(package)
            db.session.commit()

            flash(f'升级包 "{package_type}" 创建成功', 'success')
            return redirect(url_for('admin_upgrade_detail', upgrade_id=upgrade.id))

        except Exception as e:
            app.logger.error(f"Error creating package: {e}")
            db.session.rollback()
            # 清理可能已保存的文件
            if 'file_path' in locals() and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except OSError:
                    pass
            flash('创建升级包时发生错误', 'danger')

    return render_template('package_form.html', upgrade=upgrade, title='新增升级包')

@app.route('/admin/package/<int:package_id>/delete', methods=['POST'])
@login_required
def admin_package_delete(package_id):
    """删除升级包"""
    try:
        package = UpgradePackage.query.get_or_404(package_id)
        upgrade_id = package.upgrade_id
        package_name = package.original_filename

        # 获取文件路径并验证安全性
        file_path = package.get_file_path()

        # 验证文件路径在允许的目录内
        abs_upload_folder = os.path.abspath(UPLOAD_FOLDER)
        abs_file_path = os.path.abspath(file_path)

        if not abs_file_path.startswith(abs_upload_folder):
            flash('文件路径验证失败', 'danger')
            return redirect(url_for('admin_upgrade_detail', upgrade_id=upgrade_id))

        # 删除数据库记录
        db.session.delete(package)
        db.session.commit()

        # 删除文件
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                app.logger.info(f"Deleted file: {file_path}")
            except OSError as e:
                app.logger.error(f"Error deleting file {file_path}: {e}")
                # 文件删除失败不影响数据库操作的成功

        flash(f'升级包 "{package_name}" 删除成功', 'success')
        return redirect(url_for('admin_upgrade_detail', upgrade_id=upgrade_id))

    except Exception as e:
        app.logger.error(f"Error deleting package: {e}")
        db.session.rollback()
        flash('删除升级包时发生错误', 'danger')
        return redirect(url_for('admin_upgrade_detail', upgrade_id=upgrade_id))

@app.route('/admin/device/<int:device_id>/delete', methods=['POST'])
@login_required
def admin_device_delete(device_id):
    """删除设备"""
    device = Device.query.get_or_404(device_id)

    # 删除设备目录下的所有文件
    device_path = os.path.join(UPLOAD_FOLDER, device.name)
    if os.path.exists(device_path):
        import shutil
        try:
            shutil.rmtree(device_path)
        except OSError:
            pass  # 忽略文件删除错误

    device_name = device.name
    db.session.delete(device)
    db.session.commit()

    flash(f'设备 "{device_name}" 删除成功', 'success')
    return redirect(url_for('admin_list'))

@app.route('/simulate', methods=['GET', 'POST'])
@login_required
def simulate():
    """模拟OTA查询"""
    result = None

    if request.method == 'POST':
        device_name = request.form.get('device')
        firmware_version = request.form.get('firmware')
        package_type = request.form.get('type', 'full')

        if device_name and firmware_version:
            # 模拟API调用
            with app.test_request_context(
                '/api/update',
                method='POST',
                data={
                    'device': device_name,
                    'firmware': firmware_version,
                    'type': package_type
                }
            ):
                response = api_update()
                if hasattr(response, 'get_json'):
                    result = response.get_json()

    return render_template('simulate.html', result=result)

# ============ 静态文件服务 ============

@app.route('/ota/<device_name>/<upgrade_path>/<filename>')
def download_firmware(device_name, upgrade_path, filename):
    """固件文件下载"""
    try:
        # 验证参数
        if not device_name or not upgrade_path or not filename:
            return "Invalid parameters", 400

        # 清理和验证路径组件
        safe_device_name = secure_filename(device_name)
        safe_upgrade_path = secure_filename(upgrade_path)
        safe_filename = secure_filename(filename)

        if not safe_device_name or not safe_upgrade_path or not safe_filename:
            return "Invalid path components", 400

        # 查找升级包记录
        package = UpgradePackage.query.join(DeviceUpgrade).join(Device).filter(
            Device.name == safe_device_name,
            UpgradePackage.filename == safe_filename,
            UpgradePackage.is_active == True,
            DeviceUpgrade.is_active == True,
            Device.is_active == True
        ).first()

        if not package:
            return "Package not found", 404

        # 验证升级路径匹配
        expected_path = f"{package.upgrade.from_version}_to_{package.upgrade.to_version}"
        if safe_upgrade_path != secure_filename(expected_path):
            return "Invalid upgrade path", 400

        # 构建文件路径
        file_path = os.path.join(UPLOAD_FOLDER, safe_device_name, safe_upgrade_path)
        full_file_path = os.path.join(file_path, safe_filename)

        # 验证文件路径安全性
        abs_upload_folder = os.path.abspath(UPLOAD_FOLDER)
        abs_file_path = os.path.abspath(full_file_path)

        if not abs_file_path.startswith(abs_upload_folder):
            return "Path traversal attempt detected", 400

        # 检查文件是否存在
        if not os.path.exists(full_file_path):
            return "File not found", 404

        # 记录下载日志
        log_download(package, get_client_ip(), request.headers.get('User-Agent', ''))

        # 发送文件
        return send_from_directory(file_path, safe_filename, as_attachment=True)

    except Exception as e:
        app.logger.error(f"Error in download_firmware: {e}")
        return "Internal server error", 500

@app.route('/使用说明.md')
def usage_guide():
    """使用说明文档"""
    return send_from_directory('.', '使用说明.md')

# ============ 应用启动 ============

if __name__ == '__main__':
    # 初始化数据库
    init_db()

    # 启动应用
    port = int(os.environ.get('PORT', 8000))
    debug = os.environ.get('FLASK_ENV') == 'development'

    app.run(host='0.0.0.0', port=port, debug=debug)

