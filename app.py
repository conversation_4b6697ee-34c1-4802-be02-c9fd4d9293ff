import os
import uuid
import hashlib
from datetime import datetime, timezone
from flask import Flask, request, jsonify, render_template, redirect, url_for, flash, send_from_directory
from flask_sqlalchemy import SQLAlchemy
from flask_login import Lo<PERSON><PERSON>ana<PERSON>, login_user, login_required, logout_user, current_user, UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename

# 配置
UPLOAD_FOLDER = os.path.join('static', 'ota')
ALLOWED_EXTENSIONS = {'bin', 'img', 'zip', 'tar', 'gz', 'bz2', 'xz', 'hex', 'elf', 'apk'}
DOWNLOAD_BASE_URL = os.environ.get('DOWNLOAD_BASE_URL', 'http://ota.tyw.com/ota/')
DATABASE_URI = os.environ.get('DATABASE_URL', 'sqlite:///ota_config.db')
SECRET_KEY = os.environ.get('SECRET_KEY', 'change-this-secret-key')

# Flask应用
app = Flask(__name__)
app.config.update({
    'UPLOAD_FOLDER': UPLOAD_FOLDER,
    'SQLALCHEMY_DATABASE_URI': DATABASE_URI,
    'SQLALCHEMY_TRACK_MODIFICATIONS': False,
    'SECRET_KEY': SECRET_KEY,
    'MAX_CONTENT_LENGTH': 1024 * 1024 * 1024,  # 1GB
})

# 初始化数据库
db = SQLAlchemy(app)

# 数据库模型
class User(UserMixin, db.Model):
    __tablename__ = 'users'
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    is_admin = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Device(db.Model):
    __tablename__ = 'devices'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, nullable=False)
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), nullable=False)
    last_check_time = db.Column(db.DateTime)

    # 关联版本升级
    upgrades = db.relationship('DeviceUpgrade', backref='device', lazy='dynamic', cascade='all, delete-orphan')

class DeviceUpgrade(db.Model):
    __tablename__ = 'device_upgrades'
    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.Integer, db.ForeignKey('devices.id'), nullable=False)
    from_version = db.Column(db.String(64), nullable=False)
    to_version = db.Column(db.String(64), nullable=False)
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), nullable=False)
    download_count = db.Column(db.Integer, default=0, nullable=False)

    # 关联升级包
    packages = db.relationship('UpgradePackage', backref='upgrade', lazy='dynamic', cascade='all, delete-orphan')

    def get_storage_path(self):
        """获取存储路径"""
        return os.path.join(UPLOAD_FOLDER, self.device.name, f"{self.from_version}_to_{self.to_version}")

class UpgradePackage(db.Model):
    __tablename__ = 'upgrade_packages'
    id = db.Column(db.Integer, primary_key=True)
    upgrade_id = db.Column(db.Integer, db.ForeignKey('device_upgrades.id'), nullable=False)
    package_type = db.Column(db.String(32), nullable=False)  # full, incremental, patch等
    filename = db.Column(db.String(256), nullable=False)
    original_filename = db.Column(db.String(256), nullable=False)
    file_size = db.Column(db.BigInteger)
    file_md5 = db.Column(db.String(32))
    file_sha256 = db.Column(db.String(64))
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    download_count = db.Column(db.Integer, default=0, nullable=False)

    def get_file_path(self):
        """获取文件完整路径"""
        return os.path.join(self.upgrade.get_storage_path(), self.filename)

    def get_download_url(self):
        """获取下载URL"""
        return f"{DOWNLOAD_BASE_URL}{self.upgrade.device.name}/{self.upgrade.from_version}_to_{self.upgrade.to_version}/{self.filename}"

class DownloadLog(db.Model):
    __tablename__ = 'download_logs'
    id = db.Column(db.Integer, primary_key=True)
    package_id = db.Column(db.Integer, db.ForeignKey('upgrade_packages.id'), nullable=False)
    client_ip = db.Column(db.String(45))
    user_agent = db.Column(db.String(256))
    download_time = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)

    package = db.relationship('UpgradePackage', backref='download_logs')

# Flask-Login 配置
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = '请先登录'
login_manager.login_message_category = 'warning'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# 工具函数
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def generate_unique_filename(original_filename, prefix=None):
    if not original_filename:
        return None

    ext = ''
    if '.' in original_filename:
        ext = '.' + original_filename.rsplit('.', 1)[1].lower()

    unique_id = uuid.uuid4().hex[:8]

    if prefix:
        safe_prefix = secure_filename(prefix)
        filename = f"{safe_prefix}_{unique_id}{ext}"
    else:
        filename = f"firmware_{unique_id}{ext}"

    return filename

def create_storage_directory(device_name, from_version, to_version):
    """创建存储目录"""
    storage_path = os.path.join(UPLOAD_FOLDER, device_name, f"{from_version}_to_{to_version}")
    os.makedirs(storage_path, exist_ok=True)
    return storage_path

def calculate_file_md5(file_path):
    if not os.path.exists(file_path):
        return None

    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def get_client_ip():
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr

def version_compare(version1, version2):
    """版本号比较
    返回值:
    -1: version1 < version2 (需要更新)
     0: version1 == version2 (相同版本)
     1: version1 > version2 (当前版本更新)
    """
    def normalize_version(v):
        """标准化版本号"""
        import re
        # 移除前缀字母，只保留数字和点
        normalized = re.sub(r'[^\d.]', '', str(v))
        # 分割并转换为整数列表
        parts = []
        for part in normalized.split('.'):
            if part.isdigit():
                parts.append(int(part))
        return parts if parts else [0]

    try:
        v1_parts = normalize_version(version1)
        v2_parts = normalize_version(version2)

        # 补齐长度
        max_len = max(len(v1_parts), len(v2_parts))
        v1_parts.extend([0] * (max_len - len(v1_parts)))
        v2_parts.extend([0] * (max_len - len(v2_parts)))

        # 逐位比较
        for i in range(max_len):
            if v1_parts[i] < v2_parts[i]:
                return -1
            elif v1_parts[i] > v2_parts[i]:
                return 1

        return 0
    except Exception:
        # 如果无法解析版本号，则按字符串比较
        if version1 < version2:
            return -1
        elif version1 > version2:
            return 1
        else:
            return 0

def log_download(package, client_ip, user_agent):
    log = DownloadLog(
        package_id=package.id,
        client_ip=client_ip,
        user_agent=user_agent
    )
    db.session.add(log)
    package.download_count += 1
    package.upgrade.download_count += 1
    package.upgrade.device.last_check_time = datetime.now(timezone.utc)
    db.session.commit()

def find_upgrade_path(device, current_version):
    """查找升级路径"""
    # 查找从当前版本开始的升级路径
    upgrade = DeviceUpgrade.query.filter_by(
        device_id=device.id,
        from_version=current_version,
        is_active=True
    ).first()

    return upgrade

# 数据库初始化
def init_db():
    with app.app_context():
        # 删除所有表并重新创建（因为结构变化较大）
        db.drop_all()
        db.create_all()

        admin_username = os.environ.get('ADMIN_USERNAME', 'admin')
        admin_password = os.environ.get('ADMIN_PASSWORD', '123456')

        # 创建默认管理员账号
        admin = User(username=admin_username, is_admin=True)
        admin.set_password(admin_password)
        db.session.add(admin)
        db.session.commit()
        print(f'Created admin user: {admin_username}')

        # 创建示例设备和升级路径
        device = Device(
            name='example_device',
            description='示例设备',
            is_active=True
        )
        db.session.add(device)
        db.session.commit()

        print('Database initialized with new structure')

# ============ 路由 ============

@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('admin_list'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('admin_list'))

    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            login_user(user)
            flash('登录成功', 'success')
            return redirect(request.args.get('next') or url_for('admin_list'))
        else:
            flash('用户名或密码错误', 'danger')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('已登出', 'info')
    return redirect(url_for('login'))

# ============ OTA API 接口 ============

@app.route('/api/update', methods=['GET', 'POST'])
def api_update():
    """OTA更新检查接口"""
    device_name = request.values.get('device')
    current_version = request.values.get('firmware') or request.values.get('version')
    package_type = request.values.get('type', 'full')  # full, incremental, patch

    if not device_name or not current_version:
        return jsonify({'update': False, 'reason': 'missing parameters'})

    # 查找设备
    device = Device.query.filter_by(name=device_name, is_active=True).first()
    if not device:
        return jsonify({'update': False, 'reason': 'unknown device'})

    # 更新检查时间
    device.last_check_time = datetime.now(timezone.utc)

    # 查找升级路径
    upgrade = find_upgrade_path(device, current_version)
    if not upgrade:
        db.session.commit()
        return jsonify({
            'update': False,
            'message': 'No upgrade path available',
            'current_version': current_version
        })

    # 查找对应类型的升级包
    package = UpgradePackage.query.filter_by(
        upgrade_id=upgrade.id,
        package_type=package_type,
        is_active=True
    ).first()

    if not package:
        # 如果没有指定类型的包，尝试查找full包
        package = UpgradePackage.query.filter_by(
            upgrade_id=upgrade.id,
            package_type='full',
            is_active=True
        ).first()

    if not package:
        db.session.commit()
        return jsonify({
            'update': False,
            'message': 'No upgrade package available',
            'current_version': current_version
        })

    # 记录下载日志
    log_download(package, get_client_ip(), request.headers.get('User-Agent', ''))

    return jsonify({
        'update': True,
        'from_version': upgrade.from_version,
        'to_version': upgrade.to_version,
        'package_type': package.package_type,
        'url': package.get_download_url(),
        'file_size': package.file_size,
        'md5': package.file_md5,
        'sha256': package.file_sha256,
        'description': upgrade.description or package.description
    })

# 兼容旧版本API
@app.route('/update', methods=['GET', 'POST'])
def update():
    """兼容旧版本的更新接口"""
    return api_update()

# ============ 管理界面路由 ============

@app.route('/admin')
@login_required
def admin_list():
    """设备列表页面"""
    devices = Device.query.order_by(Device.updated_at.desc()).all()

    # 计算存储统计
    total_size = 0
    file_count = 0
    upgrade_count = 0

    for device in devices:
        upgrade_count += device.upgrades.count()
        for upgrade in device.upgrades:
            for package in upgrade.packages:
                if package.file_size:
                    total_size += package.file_size
                file_count += 1

    storage_stats = {
        'total_size': total_size,
        'total_size_formatted': f"{total_size / 1024 / 1024:.1f} MB" if total_size > 0 else "0 MB",
        'file_count': file_count,
        'upgrade_count': upgrade_count
    }

    return render_template('device_list.html',
                         devices=devices,
                         storage_stats=storage_stats)

@app.route('/admin/device/new', methods=['GET', 'POST'])
@login_required
def admin_device_new():
    """新增设备"""
    if request.method == 'POST':
        name = request.form['name'].strip()
        description = request.form.get('description', '').strip()

        if not name:
            flash('设备名称不能为空', 'danger')
            return render_template('device_form.html')

        if Device.query.filter_by(name=name).first():
            flash('设备名称已存在', 'danger')
            return render_template('device_form.html')

        # 创建设备记录
        device = Device(
            name=name,
            description=description,
            is_active=True
        )

        db.session.add(device)
        db.session.commit()

        flash(f'设备 "{device.name}" 创建成功', 'success')
        return redirect(url_for('admin_device_detail', device_id=device.id))

    return render_template('device_form.html', title='新增设备')

@app.route('/admin/device/<int:device_id>')
@login_required
def admin_device_detail(device_id):
    """设备详情页面"""
    device = Device.query.get_or_404(device_id)
    upgrades = device.upgrades.order_by(DeviceUpgrade.created_at.desc()).all()

    return render_template('device_detail.html', device=device, upgrades=upgrades)

@app.route('/admin/device/<int:device_id>/edit', methods=['GET', 'POST'])
@login_required
def admin_device_edit(device_id):
    """编辑设备"""
    device = Device.query.get_or_404(device_id)

    if request.method == 'POST':
        name = request.form['name'].strip()
        description = request.form.get('description', '').strip()
        is_active = 'is_active' in request.form

        if not name:
            flash('设备名称不能为空', 'danger')
            return render_template('device_form.html', device=device)

        # 检查名称唯一性（排除自己）
        existing = Device.query.filter_by(name=name).first()
        if existing and existing.id != device.id:
            flash('设备名称已存在', 'danger')
            return render_template('device_form.html', device=device)

        # 更新基本信息
        device.name = name
        device.description = description
        device.is_active = is_active

        db.session.commit()
        flash(f'设备 "{device.name}" 更新成功', 'success')
        return redirect(url_for('admin_device_detail', device_id=device.id))

    return render_template('device_form.html', device=device, title='编辑设备')

@app.route('/admin/device/<int:device_id>/upgrade/new', methods=['GET', 'POST'])
@login_required
def admin_upgrade_new(device_id):
    """新增升级路径"""
    device = Device.query.get_or_404(device_id)

    if request.method == 'POST':
        from_version = request.form['from_version'].strip()
        to_version = request.form['to_version'].strip()
        description = request.form.get('description', '').strip()

        if not from_version or not to_version:
            flash('起始版本和目标版本不能为空', 'danger')
            return render_template('upgrade_form.html', device=device)

        # 检查升级路径是否已存在
        existing = DeviceUpgrade.query.filter_by(
            device_id=device.id,
            from_version=from_version,
            to_version=to_version
        ).first()

        if existing:
            flash('该升级路径已存在', 'danger')
            return render_template('upgrade_form.html', device=device)

        # 创建升级记录
        upgrade = DeviceUpgrade(
            device_id=device.id,
            from_version=from_version,
            to_version=to_version,
            description=description,
            is_active=True
        )

        db.session.add(upgrade)
        db.session.commit()

        flash(f'升级路径 "{from_version} → {to_version}" 创建成功', 'success')
        return redirect(url_for('admin_upgrade_detail', upgrade_id=upgrade.id))

    return render_template('upgrade_form.html', device=device, title='新增升级路径')

@app.route('/admin/upgrade/<int:upgrade_id>')
@login_required
def admin_upgrade_detail(upgrade_id):
    """升级详情页面"""
    upgrade = DeviceUpgrade.query.get_or_404(upgrade_id)
    packages = upgrade.packages.order_by(UpgradePackage.created_at.desc()).all()

    return render_template('upgrade_detail.html', upgrade=upgrade, packages=packages)

@app.route('/admin/upgrade/<int:upgrade_id>/package/new', methods=['GET', 'POST'])
@login_required
def admin_package_new(upgrade_id):
    """新增升级包"""
    upgrade = DeviceUpgrade.query.get_or_404(upgrade_id)

    if request.method == 'POST':
        package_type = request.form['package_type'].strip()
        description = request.form.get('description', '').strip()
        firmware_file = request.files.get('firmware')

        if not package_type:
            flash('升级包类型不能为空', 'danger')
            return render_template('package_form.html', upgrade=upgrade)

        if not firmware_file or not firmware_file.filename:
            flash('请选择固件文件', 'danger')
            return render_template('package_form.html', upgrade=upgrade)

        if not allowed_file(firmware_file.filename):
            flash('不支持的文件类型', 'danger')
            return render_template('package_form.html', upgrade=upgrade)

        # 创建存储目录
        storage_path = create_storage_directory(
            upgrade.device.name,
            upgrade.from_version,
            upgrade.to_version
        )

        # 生成文件名
        filename = generate_unique_filename(firmware_file.filename, package_type)
        file_path = os.path.join(storage_path, filename)

        # 保存文件
        firmware_file.save(file_path)

        # 计算文件信息
        file_size = os.path.getsize(file_path)
        file_md5 = calculate_file_md5(file_path)

        # 创建升级包记录
        package = UpgradePackage(
            upgrade_id=upgrade.id,
            package_type=package_type,
            filename=filename,
            original_filename=firmware_file.filename,
            file_size=file_size,
            file_md5=file_md5,
            description=description,
            is_active=True
        )

        db.session.add(package)
        db.session.commit()

        flash(f'升级包 "{package_type}" 创建成功', 'success')
        return redirect(url_for('admin_upgrade_detail', upgrade_id=upgrade.id))

    return render_template('package_form.html', upgrade=upgrade, title='新增升级包')

@app.route('/admin/package/<int:package_id>/delete', methods=['POST'])
@login_required
def admin_package_delete(package_id):
    """删除升级包"""
    package = UpgradePackage.query.get_or_404(package_id)
    upgrade_id = package.upgrade_id

    # 删除文件
    file_path = package.get_file_path()
    if os.path.exists(file_path):
        try:
            os.remove(file_path)
        except OSError:
            pass  # 忽略文件删除错误

    package_name = package.original_filename
    db.session.delete(package)
    db.session.commit()

    flash(f'升级包 "{package_name}" 删除成功', 'success')
    return redirect(url_for('admin_upgrade_detail', upgrade_id=upgrade_id))

@app.route('/admin/device/<int:device_id>/delete', methods=['POST'])
@login_required
def admin_device_delete(device_id):
    """删除设备"""
    device = Device.query.get_or_404(device_id)

    # 删除设备目录下的所有文件
    device_path = os.path.join(UPLOAD_FOLDER, device.name)
    if os.path.exists(device_path):
        import shutil
        try:
            shutil.rmtree(device_path)
        except OSError:
            pass  # 忽略文件删除错误

    device_name = device.name
    db.session.delete(device)
    db.session.commit()

    flash(f'设备 "{device_name}" 删除成功', 'success')
    return redirect(url_for('admin_list'))

@app.route('/simulate', methods=['GET', 'POST'])
@login_required
def simulate():
    """模拟OTA查询"""
    result = None

    if request.method == 'POST':
        device_name = request.form.get('device')
        firmware_version = request.form.get('firmware')

        if device_name and firmware_version:
            # 模拟API调用
            with app.test_request_context(
                '/api/update',
                method='POST',
                data={
                    'device': device_name,
                    'firmware': firmware_version
                }
            ):
                response = api_update()
                if hasattr(response, 'get_json'):
                    result = response.get_json()

    return render_template('simulate.html', result=result)

# ============ 静态文件服务 ============

@app.route('/ota/<device_name>/<upgrade_path>/<filename>')
def download_firmware(device_name, upgrade_path, filename):
    """固件文件下载"""
    # 记录下载
    package = UpgradePackage.query.join(DeviceUpgrade).join(Device).filter(
        Device.name == device_name,
        UpgradePackage.filename == filename
    ).first()

    if package:
        log_download(package, get_client_ip(), request.headers.get('User-Agent', ''))

    file_path = os.path.join(UPLOAD_FOLDER, device_name, upgrade_path)
    return send_from_directory(file_path, filename)

@app.route('/使用说明.md')
def usage_guide():
    """使用说明文档"""
    return send_from_directory('.', '使用说明.md')

# ============ 应用启动 ============

if __name__ == '__main__':
    # 初始化数据库
    init_db()

    # 启动应用
    port = int(os.environ.get('PORT', 8000))
    debug = os.environ.get('FLASK_ENV') == 'development'

    app.run(host='0.0.0.0', port=port, debug=debug)

