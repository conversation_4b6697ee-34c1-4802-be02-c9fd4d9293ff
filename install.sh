#!/bin/bash

# OTA服务器简单安装脚本
# 直接在当前目录运行，不创建额外用户

set -e

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_info "OTA服务器安装脚本"
echo "当前目录: $(pwd)"
echo "当前用户: $(whoami)"

# 1. 检查Python
if ! command -v python3 &> /dev/null; then
    log_error "未找到python3，请先安装Python 3.8+"
    exit 1
fi

# 2. 创建虚拟环境
if [ ! -d "venv" ]; then
    log_info "创建Python虚拟环境..."
    python3 -m venv venv
fi

# 3. 安装依赖
log_info "安装Python依赖..."
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt

# 4. 创建必要目录
log_info "创建必要目录..."
mkdir -p static/ota
mkdir -p instance
mkdir -p logs

# 5. 配置环境变量
if [ ! -f ".env" ]; then
    log_info "创建环境配置..."
    
    read -p "请输入域名或IP地址 [localhost]: " DOMAIN
    DOMAIN=${DOMAIN:-localhost}
    
    read -p "请输入管理员用户名 [admin]: " ADMIN_USER
    ADMIN_USER=${ADMIN_USER:-admin}
    
    read -p "请输入管理员密码 [123456]: " ADMIN_PASSWORD
    ADMIN_PASSWORD=${ADMIN_PASSWORD:-123456}
    
    # 生成密钥
    SECRET_KEY=$(openssl rand -hex 32 2>/dev/null || echo "change-this-secret-key-$(date +%s)")
    
    cat > .env << EOF
SECRET_KEY=$SECRET_KEY
FLASK_ENV=production
DATABASE_URL=sqlite:///instance/ota_config.db
UPLOAD_FOLDER=$(pwd)/static/ota
DOWNLOAD_BASE_URL=http://$DOMAIN:8000/ota/
PORT=8000
ADMIN_USERNAME=$ADMIN_USER
ADMIN_PASSWORD=$ADMIN_PASSWORD
LOG_LEVEL=INFO
LOG_FILE=$(pwd)/logs/ota_server.log
EOF
    
    chmod 600 .env
    log_info "环境配置已创建: .env"
else
    log_info "环境配置已存在: .env"
fi

# 6. 创建启动脚本
log_info "创建启动脚本..."
cat > run.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
source venv/bin/activate
source .env
python start.py
EOF

chmod +x run.sh

# 7. 创建systemd服务（可选）
if command -v systemctl &> /dev/null && [ "$EUID" -eq 0 ]; then
    log_info "创建systemd服务..."
    
    cat > /etc/systemd/system/ota-server.service << EOF
[Unit]
Description=OTA Server
After=network.target

[Service]
Type=simple
User=$(whoami)
Group=$(id -gn)
WorkingDirectory=$(pwd)
Environment=PATH=$(pwd)/venv/bin
EnvironmentFile=$(pwd)/.env
ExecStart=$(pwd)/venv/bin/python $(pwd)/start.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable ota-server
    log_info "systemd服务已创建，可使用: sudo systemctl start ota-server"
fi

# 8. 创建nginx配置（可选）
if command -v nginx &> /dev/null && [ "$EUID" -eq 0 ]; then
    log_info "创建nginx配置..."
    
    source .env
    
    cat > /etc/nginx/sites-available/ota-server << EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    client_max_body_size 1G;
    
    location /ota/ {
        alias $(pwd)/static/ota/;
        expires 1d;
    }
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

    ln -sf /etc/nginx/sites-available/ota-server /etc/nginx/sites-enabled/
    nginx -t && systemctl reload nginx
    log_info "nginx配置已创建"
fi

log_info "安装完成！"
echo
echo "=================================="
echo "启动方式："
echo "1. 直接启动: ./run.sh"
echo "2. 后台启动: nohup ./run.sh > ota.log 2>&1 &"
if command -v systemctl &> /dev/null && [ "$EUID" -eq 0 ]; then
echo "3. 系统服务: sudo systemctl start ota-server"
fi
echo
echo "访问地址: http://$(source .env && echo $DOWNLOAD_BASE_URL | sed 's|/ota/||')"
echo "管理员账号: $(source .env && echo $ADMIN_USERNAME) / $(source .env && echo $ADMIN_PASSWORD)"
echo "=================================="
