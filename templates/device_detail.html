{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-device-hdd"></i> {{ device.name }}</h2>
    <div>
        <a href="{{ url_for('admin_upgrade_new', device_id=device.id) }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> 添加升级路径
        </a>
        <a href="{{ url_for('admin_device_edit', device_id=device.id) }}" class="btn btn-outline-secondary">
            <i class="bi bi-pencil"></i> 编辑设备
        </a>
        <a href="{{ url_for('admin_list') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> 返回列表
        </a>
    </div>
</div>

<!-- 设备信息 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">设备信息</h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">设备名称:</dt>
                    <dd class="col-sm-9">{{ device.name }}</dd>
                    
                    <dt class="col-sm-3">设备描述:</dt>
                    <dd class="col-sm-9">{{ device.description or '无' }}</dd>
                    
                    <dt class="col-sm-3">状态:</dt>
                    <dd class="col-sm-9">
                        {% if device.is_active %}
                            <span class="badge bg-success">活跃</span>
                        {% else %}
                            <span class="badge bg-danger">禁用</span>
                        {% endif %}
                    </dd>
                    
                    <dt class="col-sm-3">创建时间:</dt>
                    <dd class="col-sm-9">{{ device.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</dd>
                    
                    <dt class="col-sm-3">最后检查:</dt>
                    <dd class="col-sm-9">
                        {% if device.last_check_time %}
                            {{ device.last_check_time.strftime('%Y-%m-%d %H:%M:%S') }}
                        {% else %}
                            从未
                        {% endif %}
                    </dd>
                </dl>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">统计信息</h6>
            </div>
            <div class="card-body text-center">
                <div class="row">
                    <div class="col-6">
                        <h4 class="text-primary">{{ upgrades|length }}</h4>
                        <small class="text-muted">升级路径</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">
                            {% set package_count = 0 %}
                            {% for upgrade in upgrades %}
                                {% set package_count = package_count + upgrade.packages.count() %}
                            {% endfor %}
                            {{ package_count }}
                        </h4>
                        <small class="text-muted">升级包</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 升级路径列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">升级路径</h5>
    </div>
    <div class="card-body p-0">
        {% if upgrades %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>升级路径</th>
                        <th>描述</th>
                        <th>升级包数量</th>
                        <th>下载次数</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for upgrade in upgrades %}
                    <tr>
                        <td>
                            <strong>{{ upgrade.from_version }}</strong>
                            <i class="bi bi-arrow-right text-muted"></i>
                            <strong>{{ upgrade.to_version }}</strong>
                        </td>
                        <td>{{ upgrade.description or '-' }}</td>
                        <td>
                            <span class="badge bg-secondary">{{ upgrade.packages.count() }}</span>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ upgrade.download_count }}</span>
                        </td>
                        <td>
                            {% if upgrade.is_active %}
                                <span class="badge bg-success">活跃</span>
                            {% else %}
                                <span class="badge bg-danger">禁用</span>
                            {% endif %}
                        </td>
                        <td>
                            <small>{{ upgrade.created_at.strftime('%m-%d %H:%M') }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('admin_upgrade_detail', upgrade_id=upgrade.id) }}" 
                                   class="btn btn-outline-primary" title="查看详情">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{{ url_for('admin_package_new', upgrade_id=upgrade.id) }}" 
                                   class="btn btn-outline-success" title="添加升级包">
                                    <i class="bi bi-plus"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-arrow-up-circle text-muted" style="font-size: 3rem;"></i>
            <h5 class="text-muted mt-3">暂无升级路径</h5>
            <p class="text-muted">点击上方按钮添加第一个升级路径</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
