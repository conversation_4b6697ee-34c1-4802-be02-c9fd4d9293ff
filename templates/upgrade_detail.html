{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-arrow-up-circle"></i> 
        {{ upgrade.from_version }} → {{ upgrade.to_version }}
    </h2>
    <div>
        <a href="{{ url_for('admin_package_new', upgrade_id=upgrade.id) }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> 添加升级包
        </a>
        <a href="{{ url_for('admin_device_detail', device_id=upgrade.device.id) }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> 返回设备
        </a>
    </div>
</div>

<!-- 升级路径信息 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">升级路径信息</h5>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">设备名称:</dt>
                    <dd class="col-sm-9">{{ upgrade.device.name }}</dd>
                    
                    <dt class="col-sm-3">升级路径:</dt>
                    <dd class="col-sm-9">
                        <span class="badge bg-info">{{ upgrade.from_version }}</span>
                        <i class="bi bi-arrow-right"></i>
                        <span class="badge bg-success">{{ upgrade.to_version }}</span>
                    </dd>
                    
                    <dt class="col-sm-3">升级描述:</dt>
                    <dd class="col-sm-9">{{ upgrade.description or '无' }}</dd>
                    
                    <dt class="col-sm-3">状态:</dt>
                    <dd class="col-sm-9">
                        {% if upgrade.is_active %}
                            <span class="badge bg-success">活跃</span>
                        {% else %}
                            <span class="badge bg-danger">禁用</span>
                        {% endif %}
                    </dd>
                    
                    <dt class="col-sm-3">创建时间:</dt>
                    <dd class="col-sm-9">{{ upgrade.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</dd>
                    
                    <dt class="col-sm-3">下载次数:</dt>
                    <dd class="col-sm-9">{{ upgrade.download_count }}</dd>
                </dl>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">存储路径</h6>
            </div>
            <div class="card-body">
                <p class="small text-muted">升级包存储在:</p>
                <code class="small">
                    /ota/{{ upgrade.device.name }}/{{ upgrade.from_version }}_to_{{ upgrade.to_version }}/
                </code>
            </div>
        </div>
    </div>
</div>

<!-- 升级包列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">升级包列表</h5>
    </div>
    <div class="card-body p-0">
        {% if packages %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>包类型</th>
                        <th>文件名</th>
                        <th>原始文件名</th>
                        <th>文件大小</th>
                        <th>MD5</th>
                        <th>下载次数</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for package in packages %}
                    <tr>
                        <td>
                            {% if package.package_type == 'full' %}
                                <span class="badge bg-primary">完整包</span>
                            {% elif package.package_type == 'incremental' %}
                                <span class="badge bg-info">增量包</span>
                            {% elif package.package_type == 'patch' %}
                                <span class="badge bg-warning">补丁包</span>
                            {% elif package.package_type == 'recovery' %}
                                <span class="badge bg-danger">恢复包</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ package.package_type }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <code class="small">{{ package.filename }}</code>
                        </td>
                        <td>
                            <small>{{ package.original_filename }}</small>
                        </td>
                        <td>
                            {% if package.file_size %}
                                {{ "%.1f"|format(package.file_size / 1024 / 1024) }} MB
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if package.file_md5 %}
                                <code class="small">{{ package.file_md5[:8] }}...</code>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ package.download_count }}</span>
                        </td>
                        <td>
                            {% if package.is_active %}
                                <span class="badge bg-success">活跃</span>
                            {% else %}
                                <span class="badge bg-danger">禁用</span>
                            {% endif %}
                        </td>
                        <td>
                            <small>{{ package.created_at.strftime('%m-%d %H:%M') }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ package.get_download_url() }}" 
                                   class="btn btn-outline-success" title="下载" target="_blank">
                                    <i class="bi bi-download"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="confirmDeletePackage('{{ package.original_filename }}', {{ package.id }})" title="删除">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-file-earmark text-muted" style="font-size: 3rem;"></i>
            <h5 class="text-muted mt-3">暂无升级包</h5>
            <p class="text-muted">点击上方按钮添加第一个升级包</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deletePackageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除升级包 <strong id="packageName"></strong> 吗？</p>
                <p class="text-danger"><small>此操作将删除文件，且无法恢复。</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deletePackageForm" method="post" style="display: inline;">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDeletePackage(packageName, packageId) {
    document.getElementById('packageName').textContent = packageName;
    document.getElementById('deletePackageForm').action = '/admin/package/' + packageId + '/delete';
    new bootstrap.Modal(document.getElementById('deletePackageModal')).show();
}
</script>
{% endblock %}
