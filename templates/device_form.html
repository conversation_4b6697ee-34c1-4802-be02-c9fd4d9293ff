{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-{{ 'pencil' if device else 'plus-circle' }}"></i> 
        {{ title or ('编辑设备' if device else '新增设备') }}
    </h2>
    <a href="{{ url_for('admin_list') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left"></i> 返回列表
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">设备信息</h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">设备名称 *</label>
                                <input type="text" name="name" class="form-control" required 
                                       value="{{ device.name if device else '' }}"
                                       placeholder="输入设备名称">
                                <div class="form-text">设备的唯一标识符</div>
                            </div>
                        </div>

                    </div>

                    <div class="mb-3">
                        <label class="form-label">设备描述</label>
                        <textarea name="description" class="form-control" rows="3" 
                                  placeholder="输入设备描述（可选）">{{ device.description if device else '' }}</textarea>
                    </div>



                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" name="is_active" class="form-check-input" 
                                   {{ 'checked' if not device or device.is_active else '' }}>
                            <label class="form-check-label">启用设备</label>
                            <div class="form-text">禁用后设备无法获取更新</div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end">
                        <a href="{{ url_for('admin_list') }}" class="btn btn-secondary me-2">
                            <i class="bi bi-x-circle"></i> 取消
                        </a>
                        <button type="submit" id="submitBtn" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i>
                            {{ '更新设备' if device else '创建设备' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        {% if device %}
        <!-- 设备统计 -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">设备统计</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ device.upgrades.count() }}</h4>
                        <small class="text-muted">升级路径</small>
                    </div>
                    <div class="col-6">
                        {% set package_count = 0 %}
                        {% for upgrade in device.upgrades %}
                            {% set package_count = package_count + upgrade.packages.count() %}
                        {% endfor %}
                        <h4 class="text-info">{{ package_count }}</h4>
                        <small class="text-muted">升级包</small>
                    </div>
                </div>
                <hr>
                <div class="small">
                    <div class="d-flex justify-content-between">
                        <span>创建时间:</span>
                        <span>{{ device.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>更新时间:</span>
                        <span>{{ device.updated_at.strftime('%Y-%m-%d %H:%M') }}</span>
                    </div>
                    {% if device.last_check_time %}
                    <div class="d-flex justify-content-between">
                        <span>最后检查:</span>
                        <span>{{ device.last_check_time.strftime('%Y-%m-%d %H:%M') }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 帮助信息 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-question-circle"></i> 帮助信息</h6>
            </div>
            <div class="card-body">
                <h6>设备名称</h6>
                <p class="small text-muted">设备的唯一标识符，用于OTA更新时识别设备。</p>

                <h6>升级管理</h6>
                <p class="small text-muted">创建设备后，可以为设备添加多个版本升级路径，每个路径可以包含多种类型的升级包。</p>

                <h6>文件存储</h6>
                <p class="small text-muted">升级包按设备名称和升级路径组织存储，便于管理和维护。</p>
            </div>
        </div>
    </div>
</div>


{% endblock %}
