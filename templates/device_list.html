{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-list-ul"></i> 设备管理</h2>
    <a href="{{ url_for('admin_device_new') }}" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i> 添加设备
    </a>
</div>

<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card border-primary">
            <div class="card-body text-center">
                <h5 class="card-title">总设备数</h5>
                <h2 class="text-primary">{{ devices|length }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-info">
            <div class="card-body text-center">
                <h5 class="card-title">存储使用</h5>
                <h2 class="text-info">{{ storage_stats.total_size_formatted }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-success">
            <div class="card-body text-center">
                <h5 class="card-title">升级路径</h5>
                <h2 class="text-success">{{ storage_stats.upgrade_count }}</h2>
            </div>
        </div>
    </div>
</div>

<!-- 设备列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">设备列表</h5>
    </div>
    <div class="card-body p-0">
        {% if devices %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>设备名称</th>
                        <th>升级路径</th>
                        <th>升级包数</th>
                        <th>状态</th>
                        <th>最后检查</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for device in devices %}
                    <tr>
                        <td>
                            <strong>{{ device.name }}</strong>
                            {% if device.description %}
                                <br><small class="text-muted">{{ device.description }}</small>
                            {% endif %}
                        </td>
                        <td>
                            {% if device.upgrades.count() > 0 %}
                                <span class="badge bg-info">{{ device.upgrades.count() }} 个路径</span>
                            {% else %}
                                <span class="text-muted">无升级路径</span>
                            {% endif %}
                        </td>
                        <td>
                            {% set package_count = 0 %}
                            {% for upgrade in device.upgrades %}
                                {% set package_count = package_count + upgrade.packages.count() %}
                            {% endfor %}
                            {% if package_count > 0 %}
                                <span class="badge bg-secondary">{{ package_count }} 个包</span>
                            {% else %}
                                <span class="text-muted">无升级包</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if device.is_active %}
                                <span class="badge bg-success">
                                    <i class="bi bi-check-circle"></i> 活跃
                                </span>
                            {% else %}
                                <span class="badge bg-danger">
                                    <i class="bi bi-x-circle"></i> 禁用
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if device.last_check_time %}
                                <small>{{ device.last_check_time.strftime('%m-%d %H:%M') }}</small>
                            {% else %}
                                <span class="text-muted">从未</span>
                            {% endif %}
                        </td>
                        <td>
                            <small>{{ device.created_at.strftime('%m-%d %H:%M') }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('admin_device_detail', device_id=device.id) }}"
                                   class="btn btn-outline-success" title="查看详情">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{{ url_for('admin_device_edit', device_id=device.id) }}"
                                   class="btn btn-outline-primary" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger"
                                        onclick="confirmDelete('{{ device.name }}', {{ device.id }})" title="删除">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
            <h5 class="text-muted mt-3">暂无设备</h5>
            <p class="text-muted">点击上方按钮添加第一个设备</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除设备 <strong id="deviceName"></strong> 吗？</p>
                <p class="text-danger"><small>此操作将同时删除关联的固件文件，且无法恢复。</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(deviceName, deviceId) {
    document.getElementById('deviceName').textContent = deviceName;
    document.getElementById('deleteForm').action = '/admin/device/' + deviceId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
