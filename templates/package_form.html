{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-file-earmark-plus"></i> 
        {{ title or '新增升级包' }}
    </h2>
    <a href="{{ url_for('admin_upgrade_detail', upgrade_id=upgrade.id) }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left"></i> 返回升级路径
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">升级包信息</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    <strong>设备:</strong> {{ upgrade.device.name }} &nbsp;|&nbsp;
                    <strong>升级路径:</strong> {{ upgrade.from_version }} → {{ upgrade.to_version }}
                </div>
                
                <form method="post" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">升级包类型 *</label>
                                <select name="package_type" class="form-select" required>
                                    <option value="">请选择类型</option>
                                    <option value="full">完整包 (full)</option>
                                    <option value="incremental">增量包 (incremental)</option>
                                    <option value="patch">补丁包 (patch)</option>
                                    <option value="recovery">恢复包 (recovery)</option>
                                </select>
                                <div class="form-text">选择升级包的类型</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">升级包描述</label>
                        <textarea name="description" class="form-control" rows="3" 
                                  placeholder="描述此升级包的特点和用途（可选）"></textarea>
                    </div>

                    <!-- 固件文件上传 -->
                    <div class="mb-3">
                        <label class="form-label">固件文件 *</label>
                        
                        <div class="file-upload-area">
                            <input type="file" name="firmware" class="form-control" id="firmwareFile" required
                                   accept=".bin,.img,.zip,.tar,.gz,.bz2,.xz,.hex,.elf,.apk">
                            <div class="form-text">
                                支持格式: .bin, .img, .zip, .tar, .gz, .bz2, .xz, .hex, .elf, .apk
                            </div>
                            
                            <div class="mt-2">
                                <i class="bi bi-cloud-upload fs-1 text-muted"></i>
                                <p class="mb-0">点击选择文件或拖拽文件到此处</p>
                                <small class="text-muted">最大支持1GB文件</small>
                            </div>
                        </div>
                        
                        <!-- 上传进度条 -->
                        <div id="uploadProgress" class="mt-3" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>上传进度</span>
                                <span id="progressText">0%</span>
                            </div>
                            <div class="progress">
                                <div id="progressBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="mt-2">
                                <button type="button" id="cancelUpload" class="btn btn-sm btn-outline-danger">
                                    <i class="bi bi-x-circle"></i> 取消上传
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end">
                        <a href="{{ url_for('admin_upgrade_detail', upgrade_id=upgrade.id) }}" class="btn btn-secondary me-2">
                            <i class="bi bi-x-circle"></i> 取消
                        </a>
                        <button type="submit" id="submitBtn" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> 创建升级包
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- 存储路径预览 -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-folder"></i> 存储路径</h6>
            </div>
            <div class="card-body">
                <p class="small text-muted">文件将存储在:</p>
                <code class="small">
                    /ota/{{ upgrade.device.name }}/{{ upgrade.from_version }}_to_{{ upgrade.to_version }}/
                </code>
            </div>
        </div>

        <!-- 帮助信息 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-question-circle"></i> 升级包类型说明</h6>
            </div>
            <div class="card-body">
                <h6>完整包 (full)</h6>
                <p class="small text-muted">包含完整的固件，可以从任何版本直接升级。</p>
                
                <h6>增量包 (incremental)</h6>
                <p class="small text-muted">只包含变更的部分，文件较小，升级速度快。</p>
                
                <h6>补丁包 (patch)</h6>
                <p class="small text-muted">修复特定问题的小型更新包。</p>
                
                <h6>恢复包 (recovery)</h6>
                <p class="small text-muted">用于系统恢复的特殊固件包。</p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const fileInput = document.getElementById('firmwareFile');
    const submitBtn = document.getElementById('submitBtn');
    const uploadProgress = document.getElementById('uploadProgress');
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const cancelBtn = document.getElementById('cancelUpload');
    
    let uploadXHR = null;
    
    // 文件选择显示
    fileInput.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const fileName = this.files[0].name;
            const fileSize = (this.files[0].size / 1024 / 1024).toFixed(2);
            console.log(`Selected file: ${fileName} (${fileSize} MB)`);
        }
    });
    
    // 表单提交处理
    form.addEventListener('submit', function(e) {
        const fileInput = document.getElementById('firmwareFile');
        
        // 如果没有选择文件，使用默认提交
        if (!fileInput.files || !fileInput.files[0]) {
            return true;
        }
        
        // 如果有文件，使用AJAX上传
        e.preventDefault();
        uploadWithProgress();
    });
    
    // 取消上传
    cancelBtn.addEventListener('click', function() {
        if (uploadXHR) {
            uploadXHR.abort();
            resetUploadUI();
        }
    });
    
    function uploadWithProgress() {
        const formData = new FormData(form);
        
        // 显示进度条
        uploadProgress.style.display = 'block';
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 上传中...';
        
        uploadXHR = new XMLHttpRequest();
        
        // 上传进度
        uploadXHR.upload.addEventListener('progress', function(e) {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 100;
                progressBar.style.width = percentComplete + '%';
                progressText.textContent = Math.round(percentComplete) + '%';
            }
        });
        
        // 上传完成
        uploadXHR.addEventListener('load', function() {
            if (uploadXHR.status === 200) {
                // 成功，重定向
                window.location.href = '{{ url_for("admin_upgrade_detail", upgrade_id=upgrade.id) }}';
            } else {
                // 失败，显示错误
                alert('上传失败: ' + uploadXHR.statusText);
                resetUploadUI();
            }
        });
        
        // 上传错误
        uploadXHR.addEventListener('error', function() {
            alert('上传失败: 网络错误');
            resetUploadUI();
        });
        
        // 上传被取消
        uploadXHR.addEventListener('abort', function() {
            alert('上传已取消');
            resetUploadUI();
        });
        
        // 发送请求
        uploadXHR.open('POST', form.action);
        uploadXHR.send(formData);
    }
    
    function resetUploadUI() {
        uploadProgress.style.display = 'none';
        progressBar.style.width = '0%';
        progressText.textContent = '0%';
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="bi bi-check-circle"></i> 创建升级包';
        uploadXHR = null;
    }
});
</script>
{% endblock %}
