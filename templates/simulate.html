{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-play-circle"></i> OTA 模拟测试</h2>
</div>

<div class="row">
    <div class="col-lg-6">
        <!-- 测试表单 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">模拟设备请求</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <div class="mb-3">
                        <label class="form-label">设备名称</label>
                        <input type="text" name="device" class="form-control"
                               placeholder="输入设备名称" required>
                        <div class="form-text">输入要测试的设备名称</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">当前版本</label>
                        <input type="text" name="firmware" class="form-control"
                               placeholder="输入当前版本号" required>
                        <div class="form-text">输入设备当前的固件版本号</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">升级包类型</label>
                        <select name="type" class="form-select">
                            <option value="full">全量升级包 (full)</option>
                            <option value="incremental">增量升级包 (incremental)</option>
                            <option value="patch">补丁升级包 (patch)</option>
                            <option value="delta">差分升级包 (delta)</option>
                            <option value="bootloader">引导程序 (bootloader)</option>
                            <option value="kernel">内核 (kernel)</option>
                            <option value="rootfs">根文件系统 (rootfs)</option>
                        </select>
                        <div class="form-text">选择要测试的升级包类型</div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-play-fill"></i> 执行测试
                        </button>
                    </div>

                    <div class="d-grid mt-2">
                        <button type="button" class="btn btn-outline-secondary" onclick="testAllTypes()">
                            <i class="bi bi-list-check"></i> 测试所有类型
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- API 说明 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> API 使用说明</h6>
            </div>
            <div class="card-body">
                <h6>请求地址</h6>
                <div class="bg-light p-2 rounded mb-3">
                    <code>GET/POST {{ request.url_root }}api/update</code>
                </div>

                <h6>请求参数</h6>
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>device</code></td>
                            <td>string</td>
                            <td>设备名称（必需）</td>
                        </tr>
                        <tr>
                            <td><code>firmware</code></td>
                            <td>string</td>
                            <td>当前固件版本（必需）</td>
                        </tr>
                        <tr>
                            <td><code>type</code></td>
                            <td>string</td>
                            <td>升级包类型（可选，默认：full）</td>
                        </tr>
                    </tbody>
                </table>

                <h6>支持的升级包类型</h6>
                <ul class="list-unstyled">
                    <li><code>full</code> - 全量升级包</li>
                    <li><code>incremental</code> - 增量升级包</li>
                    <li><code>patch</code> - 补丁升级包</li>
                    <li><code>delta</code> - 差分升级包</li>
                    <li><code>bootloader</code> - 引导程序</li>
                    <li><code>kernel</code> - 内核</li>
                    <li><code>rootfs</code> - 根文件系统</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <!-- 测试结果 -->
        {% if result %}
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">测试结果</h5>
                {% if result.update %}
                    <span class="badge bg-success">需要更新</span>
                {% else %}
                    <span class="badge bg-info">无需更新</span>
                {% endif %}
            </div>
            <div class="card-body">
                <div class="bg-light p-3 rounded">
                    <pre class="mb-0"><code>{{ result | tojson(indent=2) }}</code></pre>
                </div>

                {% if result.update %}
                <div class="mt-3">
                    <h6>更新信息</h6>
                    <ul class="list-unstyled">
                        <li><strong>最新版本:</strong> {{ result.latest_version }}</li>
                        {% if result.file_size %}
                        <li><strong>文件大小:</strong> {{ "%.1f"|format(result.file_size / 1024 / 1024) }} MB</li>
                        {% endif %}
                        {% if result.md5 %}
                        <li><strong>MD5校验:</strong> <code>{{ result.md5 }}</code></li>
                        {% endif %}
                        {% if result.url %}
                        <li><strong>下载地址:</strong> <a href="{{ result.url }}" target="_blank">{{ result.url }}</a></li>
                        {% endif %}
                    </ul>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 使用示例 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-code-slash"></i> 客户端示例</h6>
            </div>
            <div class="card-body">
                <h6>curl 命令</h6>
                <div class="bg-dark text-light p-2 rounded">
                    <code>curl -X POST {{ request.url_root }}api/update \<br>
  -d "device=your_device_name" \<br>
  -d "firmware=your_current_version"</code>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testAllTypes() {
    const deviceName = document.querySelector('input[name="device"]').value;
    const firmwareVersion = document.querySelector('input[name="firmware"]').value;

    if (!deviceName || !firmwareVersion) {
        alert('请先填写设备名称和当前版本');
        return;
    }

    const types = ['full', 'incremental', 'patch', 'delta', 'bootloader', 'kernel', 'rootfs'];
    const results = [];

    // 显示加载状态
    const resultContainer = document.querySelector('.col-lg-6:last-child');
    resultContainer.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">测试所有类型</h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">正在测试所有升级包类型...</p>
                </div>
            </div>
        </div>
    `;

    // 测试所有类型
    Promise.all(types.map(type =>
        fetch('/api/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `device=${encodeURIComponent(deviceName)}&firmware=${encodeURIComponent(firmwareVersion)}&type=${type}`
        }).then(response => response.json())
          .then(data => ({ type, data }))
          .catch(error => ({ type, error: error.message }))
    )).then(results => {
        // 显示结果
        let html = `
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">所有类型测试结果</h5>
                </div>
                <div class="card-body">
        `;

        results.forEach(result => {
            const isAvailable = result.data && result.data.update;
            const badgeClass = isAvailable ? 'bg-success' : 'bg-secondary';
            const badgeText = isAvailable ? '可用' : '不可用';

            html += `
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <strong>${result.type}</strong>
                        <span class="badge ${badgeClass}">${badgeText}</span>
                    </div>
                    <div class="bg-light p-2 rounded mt-1">
                        <small><code>${JSON.stringify(result.data || result.error, null, 2)}</code></small>
                    </div>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;

        resultContainer.innerHTML = html;
    });
}
</script>
{% endblock %}

