{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-play-circle"></i> OTA 模拟测试</h2>
</div>

<div class="row">
    <div class="col-lg-6">
        <!-- 测试表单 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">模拟设备请求</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <div class="mb-3">
                        <label class="form-label">设备名称</label>
                        <input type="text" name="device" class="form-control"
                               placeholder="输入设备名称" required>
                        <div class="form-text">输入要测试的设备名称</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">当前版本</label>
                        <input type="text" name="firmware" class="form-control"
                               placeholder="输入当前版本号" required>
                        <div class="form-text">输入设备当前的固件版本号</div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-play-fill"></i> 执行测试
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- API 说明 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> API 使用说明</h6>
            </div>
            <div class="card-body">
                <h6>请求地址</h6>
                <div class="bg-light p-2 rounded mb-3">
                    <code>GET/POST {{ request.url_root }}api/update</code>
                </div>

                <h6>请求参数</h6>
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>device</code></td>
                            <td>string</td>
                            <td>设备名称（必需）</td>
                        </tr>
                        <tr>
                            <td><code>firmware</code></td>
                            <td>string</td>
                            <td>当前固件版本（必需）</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <!-- 测试结果 -->
        {% if result %}
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">测试结果</h5>
                {% if result.update %}
                    <span class="badge bg-success">需要更新</span>
                {% else %}
                    <span class="badge bg-info">无需更新</span>
                {% endif %}
            </div>
            <div class="card-body">
                <div class="bg-light p-3 rounded">
                    <pre class="mb-0"><code>{{ result | tojson(indent=2) }}</code></pre>
                </div>

                {% if result.update %}
                <div class="mt-3">
                    <h6>更新信息</h6>
                    <ul class="list-unstyled">
                        <li><strong>最新版本:</strong> {{ result.latest_version }}</li>
                        {% if result.file_size %}
                        <li><strong>文件大小:</strong> {{ "%.1f"|format(result.file_size / 1024 / 1024) }} MB</li>
                        {% endif %}
                        {% if result.md5 %}
                        <li><strong>MD5校验:</strong> <code>{{ result.md5 }}</code></li>
                        {% endif %}
                        {% if result.url %}
                        <li><strong>下载地址:</strong> <a href="{{ result.url }}" target="_blank">{{ result.url }}</a></li>
                        {% endif %}
                    </ul>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- 使用示例 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-code-slash"></i> 客户端示例</h6>
            </div>
            <div class="card-body">
                <h6>curl 命令</h6>
                <div class="bg-dark text-light p-2 rounded">
                    <code>curl -X POST {{ request.url_root }}api/update \<br>
  -d "device=your_device_name" \<br>
  -d "firmware=your_current_version"</code>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

