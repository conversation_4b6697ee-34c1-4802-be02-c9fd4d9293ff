{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-arrow-up-circle"></i> 
        {{ title or '新增升级路径' }}
    </h2>
    <a href="{{ url_for('admin_device_detail', device_id=device.id) }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left"></i> 返回设备
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">升级路径信息</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    <strong>设备:</strong> {{ device.name }}
                </div>
                
                <form method="post">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">起始版本 *</label>
                                <input type="text" name="from_version" class="form-control" required 
                                       placeholder="例如: v1.0.0">
                                <div class="form-text">设备当前版本号</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">目标版本 *</label>
                                <input type="text" name="to_version" class="form-control" required 
                                       placeholder="例如: v1.1.0">
                                <div class="form-text">升级后的版本号</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">升级描述</label>
                        <textarea name="description" class="form-control" rows="3" 
                                  placeholder="描述此次升级的内容和改进（可选）"></textarea>
                    </div>

                    <div class="d-flex justify-content-end">
                        <a href="{{ url_for('admin_device_detail', device_id=device.id) }}" class="btn btn-secondary me-2">
                            <i class="bi bi-x-circle"></i> 取消
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> 创建升级路径
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- 帮助信息 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-question-circle"></i> 升级路径说明</h6>
            </div>
            <div class="card-body">
                <h6>版本管理</h6>
                <p class="small text-muted">每个升级路径定义了从一个版本到另一个版本的升级方案。</p>
                
                <h6>升级包类型</h6>
                <ul class="small text-muted">
                    <li><strong>full</strong>: 完整固件包</li>
                    <li><strong>incremental</strong>: 增量升级包</li>
                    <li><strong>patch</strong>: 补丁包</li>
                </ul>
                
                <h6>文件存储</h6>
                <p class="small text-muted">升级包将存储在以下目录结构中：</p>
                <code class="small">
                    /ota/<br>
                    └── {{ device.name }}/<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;└── v1.0_to_v1.1/<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;├── full_xxx.bin<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;└── patch_xxx.bin
                </code>
            </div>
        </div>
    </div>
</div>
{% endblock %}
