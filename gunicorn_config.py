#!/usr/bin/env python3
"""
Gunicorn配置文件
用于生产环境部署
"""

import os
import multiprocessing

# 服务器配置
bind = f"127.0.0.1:{os.environ.get('PORT', 8000)}"
backlog = 2048

# Worker配置
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 50

# 日志配置
accesslog = "/opt/ota_server/logs/gunicorn_access.log"
errorlog = "/opt/ota_server/logs/gunicorn_error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程配置
daemon = False
pidfile = "/opt/ota_server/gunicorn.pid"
user = "ota"
group = "ota"
tmp_upload_dir = None

# 安全配置
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# 性能配置
preload_app = True
sendfile = True

# 重启配置
reload = False
reload_engine = "auto"

def when_ready(server):
    """服务器启动完成时的回调"""
    server.log.info("OTA Server is ready. Listening on: %s", server.address)

def worker_int(worker):
    """Worker进程中断时的回调"""
    worker.log.info("worker received INT or QUIT signal")

def pre_fork(server, worker):
    """Fork worker进程前的回调"""
    server.log.info("Worker spawned (pid: %s)", worker.pid)

def post_fork(server, worker):
    """Fork worker进程后的回调"""
    server.log.info("Worker spawned (pid: %s)", worker.pid)

def post_worker_init(worker):
    """Worker进程初始化后的回调"""
    worker.log.info("Worker initialized (pid: %s)", worker.pid)

def worker_abort(worker):
    """Worker进程异常退出时的回调"""
    worker.log.info("Worker aborted (pid: %s)", worker.pid)
