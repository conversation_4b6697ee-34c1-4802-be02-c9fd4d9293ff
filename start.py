#!/usr/bin/env python3
"""
简单的OTA服务器启动脚本
"""

import os
from app import app, init_db

if __name__ == '__main__':
    # 初始化数据库
    print("正在初始化数据库...")
    init_db()
    print("数据库初始化完成")
    
    # 启动应用
    port = int(os.environ.get('PORT', 8000))
    debug = os.environ.get('FLASK_ENV') == 'development'
    
    print(f"启动OTA服务器...")
    print(f"地址: http://0.0.0.0:{port}")
    print(f"调试模式: {'开启' if debug else '关闭'}")
    
    app.run(host='0.0.0.0', port=port, debug=debug)
