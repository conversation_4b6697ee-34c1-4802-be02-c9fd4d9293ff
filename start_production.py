#!/usr/bin/env python3
"""
生产环境启动脚本
使用Gunicorn作为WSGI服务器
"""

import os
import sys
import subprocess
from pathlib import Path

def check_environment():
    """检查运行环境"""
    # 检查虚拟环境
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("警告: 未检测到虚拟环境")
    
    # 检查必要文件
    required_files = ['app.py', 'gunicorn_config.py', '.env']
    for file in required_files:
        if not os.path.exists(file):
            print(f"错误: 缺少必要文件 {file}")
            sys.exit(1)
    
    # 检查日志目录
    log_dir = Path("logs")
    if not log_dir.exists():
        log_dir.mkdir(exist_ok=True)
        print("创建日志目录: logs/")

def load_env():
    """加载环境变量"""
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file) as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    os.environ[key] = value

def init_database():
    """初始化数据库"""
    try:
        from app import init_db
        print("正在初始化数据库...")
        init_db()
        print("数据库初始化完成")
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        sys.exit(1)

def start_gunicorn():
    """启动Gunicorn服务器"""
    cmd = [
        "gunicorn",
        "--config", "gunicorn_config.py",
        "app:app"
    ]
    
    print("启动Gunicorn服务器...")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Gunicorn启动失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n服务器已停止")

def main():
    """主函数"""
    print("OTA服务器生产环境启动")
    print("=" * 40)
    
    # 检查环境
    check_environment()
    
    # 加载环境变量
    load_env()
    
    # 检查是否需要初始化数据库
    if "--init-db" in sys.argv:
        init_database()
        return
    
    # 启动服务器
    start_gunicorn()

if __name__ == "__main__":
    main()
