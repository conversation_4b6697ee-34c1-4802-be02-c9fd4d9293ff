#!/bin/bash

# OTA服务器自动部署脚本
# 使用方法: sudo bash deploy.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo bash deploy.sh"
        exit 1
    fi
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        log_error "无法检测操作系统"
        exit 1
    fi
    
    log_info "检测到操作系统: $OS $VER"
}

# 安装系统依赖
install_dependencies() {
    log_step "安装系统依赖包..."
    
    if [[ $OS == *"Ubuntu"* ]] || [[ $OS == *"Debian"* ]]; then
        apt update
        apt install -y python3 python3-pip python3-venv nginx git curl wget sqlite3
    elif [[ $OS == *"CentOS"* ]] || [[ $OS == *"Red Hat"* ]]; then
        if command -v dnf &> /dev/null; then
            dnf update -y
            dnf install -y python3 python3-pip python3-venv nginx git curl wget sqlite
        else
            yum update -y
            yum install -y python3 python3-pip python3-venv nginx git curl wget sqlite
        fi
    else
        log_error "不支持的操作系统: $OS"
        exit 1
    fi
    
    log_info "系统依赖安装完成"
}

# 创建系统用户
create_user() {
    log_step "创建系统用户..."
    
    if ! id "ota" &>/dev/null; then
        useradd -r -s /bin/bash -d /opt/ota_server ota
        log_info "创建用户 ota"
    else
        log_info "用户 ota 已存在"
    fi
    
    mkdir -p /opt/ota_server
    chown ota:ota /opt/ota_server
}

# 部署应用
deploy_app() {
    log_step "部署OTA服务器应用..."
    
    # 切换到应用目录
    cd /opt/ota_server
    
    # 如果当前目录有源码，复制过去
    if [[ -f "$(dirname "$0")/app.py" ]]; then
        log_info "从当前目录复制源码..."
        cp -r "$(dirname "$0")"/* . 2>/dev/null || true
        chown -R ota:ota .
    else
        log_warn "未找到源码文件，请手动复制源码到 /opt/ota_server"
    fi
    
    # 创建虚拟环境
    sudo -u ota python3 -m venv venv
    
    # 安装Python依赖
    if [[ -f requirements.txt ]]; then
        sudo -u ota /opt/ota_server/venv/bin/pip install --upgrade pip
        sudo -u ota /opt/ota_server/venv/bin/pip install -r requirements.txt
        log_info "Python依赖安装完成"
    else
        log_warn "未找到 requirements.txt，请手动安装依赖"
    fi
    
    # 创建必要目录
    sudo -u ota mkdir -p static/ota instance logs
}

# 配置环境变量
configure_env() {
    log_step "配置环境变量..."
    
    # 生成随机密钥
    SECRET_KEY=$(openssl rand -hex 32)
    ADMIN_PASSWORD=$(openssl rand -base64 12)
    
    # 获取用户输入
    read -p "请输入域名 (例: ota.example.com): " DOMAIN
    read -p "请输入管理员用户名 [admin]: " ADMIN_USER
    ADMIN_USER=${ADMIN_USER:-admin}
    
    # 创建环境配置文件
    cat > /opt/ota_server/.env << EOF
# 基础配置
SECRET_KEY=$SECRET_KEY
FLASK_ENV=production

# 数据库配置
DATABASE_URL=sqlite:///instance/ota_config.db

# 文件配置
UPLOAD_FOLDER=/opt/ota_server/static/ota
MAX_FILE_SIZE=536870912
MAX_CONTENT_LENGTH=1073741824

# 网络配置
DOWNLOAD_BASE_URL=https://$DOMAIN/ota/
PORT=8000

# 管理员配置
ADMIN_USERNAME=$ADMIN_USER
ADMIN_PASSWORD=$ADMIN_PASSWORD

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/opt/ota_server/logs/ota_server.log
EOF

    chown ota:ota /opt/ota_server/.env
    chmod 600 /opt/ota_server/.env
    
    log_info "环境配置完成"
    log_warn "管理员密码: $ADMIN_PASSWORD (请记录此密码)"
}

# 初始化数据库
init_database() {
    log_step "初始化数据库..."
    
    cd /opt/ota_server
    sudo -u ota bash -c "source venv/bin/activate && python start.py" &
    INIT_PID=$!
    
    # 等待初始化完成
    sleep 5
    kill $INIT_PID 2>/dev/null || true
    
    log_info "数据库初始化完成"
}

# 配置systemd服务
configure_systemd() {
    log_step "配置systemd服务..."
    
    cat > /etc/systemd/system/ota-server.service << 'EOF'
[Unit]
Description=OTA Server
After=network.target

[Service]
Type=simple
User=ota
Group=ota
WorkingDirectory=/opt/ota_server
Environment=PATH=/opt/ota_server/venv/bin
EnvironmentFile=/opt/ota_server/.env
ExecStart=/opt/ota_server/venv/bin/python /opt/ota_server/start_production.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=3
StandardOutput=journal
StandardError=journal

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/opt/ota_server
ProtectHome=true

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable ota-server
    
    log_info "systemd服务配置完成"
}

# 配置nginx
configure_nginx() {
    log_step "配置nginx..."
    
    cat > /etc/nginx/sites-available/ota-server << EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    # 重定向到HTTPS (SSL配置后启用)
    # return 301 https://\$server_name\$request_uri;
    
    # 临时HTTP配置
    client_max_body_size 1G;
    
    # 静态文件服务
    location /ota/ {
        alias /opt/ota_server/static/ota/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
    
    # 代理到Flask应用
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # API接口
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
}
EOF

    # 启用配置
    ln -sf /etc/nginx/sites-available/ota-server /etc/nginx/sites-enabled/
    rm -f /etc/nginx/sites-enabled/default
    
    # 测试配置
    nginx -t
    
    systemctl enable nginx
    systemctl restart nginx
    
    log_info "nginx配置完成"
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        # Ubuntu/Debian
        ufw --force enable
        ufw allow 22/tcp
        ufw allow 80/tcp
        ufw allow 443/tcp
    elif command -v firewall-cmd &> /dev/null; then
        # CentOS/RHEL
        systemctl enable firewalld
        systemctl start firewalld
        firewall-cmd --permanent --add-service=ssh
        firewall-cmd --permanent --add-service=http
        firewall-cmd --permanent --add-service=https
        firewall-cmd --reload
    fi
    
    log_info "防火墙配置完成"
}

# 启动服务
start_services() {
    log_step "启动服务..."
    
    systemctl start ota-server
    systemctl start nginx
    
    # 检查服务状态
    if systemctl is-active --quiet ota-server; then
        log_info "OTA服务启动成功"
    else
        log_error "OTA服务启动失败"
        systemctl status ota-server
    fi
    
    if systemctl is-active --quiet nginx; then
        log_info "Nginx服务启动成功"
    else
        log_error "Nginx服务启动失败"
        systemctl status nginx
    fi
}

# 显示部署信息
show_info() {
    log_step "部署完成！"
    
    echo
    echo "=================================="
    echo "OTA服务器部署信息"
    echo "=================================="
    echo "访问地址: http://$DOMAIN"
    echo "管理员用户: $ADMIN_USER"
    echo "管理员密码: $ADMIN_PASSWORD"
    echo
    echo "服务管理命令:"
    echo "  启动服务: sudo systemctl start ota-server"
    echo "  停止服务: sudo systemctl stop ota-server"
    echo "  重启服务: sudo systemctl restart ota-server"
    echo "  查看状态: sudo systemctl status ota-server"
    echo "  查看日志: sudo journalctl -u ota-server -f"
    echo
    echo "配置文件位置:"
    echo "  应用目录: /opt/ota_server"
    echo "  环境配置: /opt/ota_server/.env"
    echo "  Nginx配置: /etc/nginx/sites-available/ota-server"
    echo "  系统服务: /etc/systemd/system/ota-server.service"
    echo
    echo "下一步:"
    echo "1. 配置SSL证书 (推荐使用Let's Encrypt)"
    echo "2. 设置定时备份"
    echo "3. 配置监控"
    echo "=================================="
}

# 主函数
main() {
    log_info "开始部署OTA服务器..."
    
    check_root
    detect_os
    install_dependencies
    create_user
    deploy_app
    configure_env
    init_database
    configure_systemd
    configure_nginx
    configure_firewall
    start_services
    show_info
    
    log_info "部署完成！"
}

# 运行主函数
main "$@"
