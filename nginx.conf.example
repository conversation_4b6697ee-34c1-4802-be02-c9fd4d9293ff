server {
    listen 80;
    server_name ota.tyw.com;  # 改为你的域名或 IP
    
    # 增加文件上传大小限制
    client_max_body_size 1G;

    # Flask 应用代理
    location / {
        proxy_pass         http://127.0.0.1:8000;
        proxy_set_header   Host $host;
        proxy_set_header   X-Real-IP $remote_addr;
        proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Proto $scheme;
        
        # 增加代理超时时间（用于大文件上传）
        proxy_read_timeout 1800;
        proxy_connect_timeout 1800;
        proxy_send_timeout 1800;
    }

    # 固件文件直接托管（推荐，性能更好）
    location /ota/ {
        alias /home/<USER>/ota_server/static/ota/;
        autoindex on;
        
        # 添加下载相关头部
        add_header Content-Disposition 'attachment';
        add_header X-Content-Type-Options nosniff;
        
        # 缓存设置
        expires 1d;
        add_header Cache-Control "public, immutable";
    }

    # 日志配置
    access_log /var/log/nginx/ota_access.log;
    error_log /var/log/nginx/ota_error.log;

    error_page 404 /404.html;
    location = /404.html {
        internal;
    }
}
