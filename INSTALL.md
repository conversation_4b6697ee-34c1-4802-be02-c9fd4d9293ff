# OTA服务器安装部署教程

本教程将指导您从零开始部署OTA服务器，包括Python虚拟环境、系统服务、nginx反向代理和systemd服务配置。

## 系统要求

- Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- Python 3.8+
- 至少 2GB RAM
- 至少 10GB 磁盘空间

## 1. 系统准备

### 1.1 更新系统包

```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
# 或者 (CentOS 8+)
sudo dnf update -y
```

### 1.2 安装必要的系统包

```bash
# Ubuntu/Debian
sudo apt install -y python3 python3-pip python3-venv nginx git curl wget

# CentOS/RHEL
sudo yum install -y python3 python3-pip python3-venv nginx git curl wget
# 或者 (CentOS 8+)
sudo dnf install -y python3 python3-pip python3-venv nginx git curl wget
```

### 1.3 创建系统用户

```bash
# 创建专用用户
sudo useradd -r -s /bin/bash -d /opt/ota_server ota
sudo mkdir -p /opt/ota_server
sudo chown ota:ota /opt/ota_server
```

## 2. 部署OTA服务器

### 2.1 切换到ota用户

```bash
sudo su - ota
cd /opt/ota_server
```

### 2.2 下载源码

```bash
# 如果有git仓库
git clone https://github.com/your-repo/ota_server.git .

# 或者解压源码包
# tar -xzf ota_server.tar.gz --strip-components=1
```

### 2.3 创建Python虚拟环境

```bash
python3 -m venv venv
source venv/bin/activate
```

### 2.4 安装Python依赖

```bash
pip install --upgrade pip
pip install -r requirements.txt
```

### 2.5 配置环境变量

```bash
# 创建环境配置文件
cat > .env << 'EOF'
# 基础配置
SECRET_KEY=your-very-secure-secret-key-change-this
FLASK_ENV=production

# 数据库配置
DATABASE_URL=sqlite:///instance/ota_config.db

# 文件配置
UPLOAD_FOLDER=/opt/ota_server/static/ota
MAX_FILE_SIZE=536870912
MAX_CONTENT_LENGTH=1073741824

# 网络配置
DOWNLOAD_BASE_URL=https://your-domain.com/ota/
PORT=8000

# 管理员配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-admin-password

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/opt/ota_server/logs/ota_server.log
EOF

# 设置文件权限
chmod 600 .env
```

### 2.6 创建必要目录

```bash
mkdir -p static/ota
mkdir -p instance
mkdir -p logs
```

### 2.7 初始化数据库

```bash
source venv/bin/activate
python start.py --init-only
```

## 3. 配置Systemd服务

### 3.1 创建systemd服务文件

```bash
sudo tee /etc/systemd/system/ota-server.service > /dev/null << 'EOF'
[Unit]
Description=OTA Server
After=network.target

[Service]
Type=simple
User=ota
Group=ota
WorkingDirectory=/opt/ota_server
Environment=PATH=/opt/ota_server/venv/bin
EnvironmentFile=/opt/ota_server/.env
ExecStart=/opt/ota_server/venv/bin/python /opt/ota_server/start.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=3
StandardOutput=journal
StandardError=journal

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/opt/ota_server
ProtectHome=true

[Install]
WantedBy=multi-user.target
EOF
```

### 3.2 启用并启动服务

```bash
sudo systemctl daemon-reload
sudo systemctl enable ota-server
sudo systemctl start ota-server
sudo systemctl status ota-server
```

## 4. 配置Nginx反向代理

### 4.1 创建nginx配置文件

```bash
sudo tee /etc/nginx/sites-available/ota-server > /dev/null << 'EOF'
server {
    listen 80;
    server_name your-domain.com;  # 替换为您的域名
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;  # 替换为您的域名
    
    # SSL证书配置 (需要您自己配置SSL证书)
    # ssl_certificate /path/to/your/certificate.crt;
    # ssl_certificate_key /path/to/your/private.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 客户端最大上传大小
    client_max_body_size 1G;
    
    # 静态文件服务
    location /ota/ {
        alias /opt/ota_server/static/ota/;
        expires 1d;
        add_header Cache-Control "public, immutable";
        
        # 安全配置
        location ~* \.(php|jsp|asp|sh|py)$ {
            deny all;
        }
    }
    
    # 代理到Flask应用
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # API接口特殊配置
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # API不缓存
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # 健康检查
    location /health {
        access_log off;
        proxy_pass http://127.0.0.1:8000/api/health;
    }
}
EOF
```

### 4.2 启用nginx配置

```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/ota-server /etc/nginx/sites-enabled/

# 删除默认配置
sudo rm -f /etc/nginx/sites-enabled/default

# 测试nginx配置
sudo nginx -t

# 重启nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## 5. 配置防火墙

```bash
# Ubuntu (ufw)
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw --force enable

# CentOS (firewalld)
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

## 6. 配置SSL证书 (可选但推荐)

### 6.1 使用Let's Encrypt (免费)

```bash
# 安装certbot
sudo apt install certbot python3-certbot-nginx  # Ubuntu/Debian
# 或
sudo yum install certbot python3-certbot-nginx  # CentOS

# 获取证书
sudo certbot --nginx -d your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## 7. 日志管理

### 7.1 配置logrotate

```bash
sudo tee /etc/logrotate.d/ota-server > /dev/null << 'EOF'
/opt/ota_server/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 ota ota
    postrotate
        systemctl reload ota-server
    endscript
}
EOF
```

## 8. 监控和维护

### 8.1 检查服务状态

```bash
# 检查OTA服务
sudo systemctl status ota-server

# 检查nginx
sudo systemctl status nginx

# 查看日志
sudo journalctl -u ota-server -f
tail -f /opt/ota_server/logs/ota_server.log
```

### 8.2 性能监控

```bash
# 检查磁盘使用
df -h /opt/ota_server

# 检查内存使用
free -h

# 检查进程
ps aux | grep python
```

## 9. 备份策略

### 9.1 创建备份脚本

```bash
sudo tee /opt/ota_server/backup.sh > /dev/null << 'EOF'
#!/bin/bash

BACKUP_DIR="/opt/backups/ota_server"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="ota_backup_${DATE}.tar.gz"

mkdir -p $BACKUP_DIR

# 停止服务
systemctl stop ota-server

# 创建备份
tar -czf $BACKUP_DIR/$BACKUP_FILE \
    --exclude='venv' \
    --exclude='__pycache__' \
    --exclude='*.pyc' \
    /opt/ota_server

# 启动服务
systemctl start ota-server

# 保留最近30天的备份
find $BACKUP_DIR -name "ota_backup_*.tar.gz" -mtime +30 -delete

echo "Backup completed: $BACKUP_DIR/$BACKUP_FILE"
EOF

chmod +x /opt/ota_server/backup.sh

# 设置定时备份
sudo crontab -e
# 添加以下行（每天凌晨2点备份）：
# 0 2 * * * /opt/ota_server/backup.sh
```

## 10. 故障排除

### 10.1 常见问题

1. **服务无法启动**
   ```bash
   sudo journalctl -u ota-server -n 50
   ```

2. **权限问题**
   ```bash
   sudo chown -R ota:ota /opt/ota_server
   sudo chmod -R 755 /opt/ota_server
   sudo chmod 600 /opt/ota_server/.env
   ```

3. **端口冲突**
   ```bash
   sudo netstat -tlnp | grep :8000
   sudo lsof -i :8000
   ```

4. **nginx配置错误**
   ```bash
   sudo nginx -t
   sudo tail -f /var/log/nginx/error.log
   ```

### 10.2 性能优化

1. **增加worker进程数**
   - 编辑 `/etc/systemd/system/ota-server.service`
   - 修改 `ExecStart` 为使用gunicorn：
   ```
   ExecStart=/opt/ota_server/venv/bin/gunicorn -w 4 -b 127.0.0.1:8000 app:app
   ```

2. **数据库优化**
   - 考虑使用PostgreSQL或MySQL替代SQLite
   - 定期清理下载日志

## 11. 安全建议

1. **定期更新系统和依赖**
2. **使用强密码和密钥**
3. **启用防火墙**
4. **配置SSL/TLS**
5. **定期备份数据**
6. **监控系统日志**
7. **限制文件上传大小**
8. **定期检查安全漏洞**

## 12. 访问服务

部署完成后，您可以通过以下方式访问：

- **管理界面**: https://your-domain.com/
- **API接口**: https://your-domain.com/api/
- **健康检查**: https://your-domain.com/health

默认管理员账号：
- 用户名: admin
- 密码: 在 `.env` 文件中设置的密码

---

**注意**: 请将 `your-domain.com` 替换为您的实际域名，并根据实际情况调整配置参数。
