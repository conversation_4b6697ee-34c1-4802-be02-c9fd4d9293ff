# OTA服务器简单部署说明

## 📦 安装包信息

- **包名**: `ota_server_simple_20250709_100449.tar.gz` (32KB)
- **运行方式**: 直接在当前用户下运行，无需创建额外用户
- **支持的升级包类型**: full（全量）、incremental（增量）、targetfiles（目标文件）

## 🚀 快速部署

### 1. 解压安装包
```bash
tar -xzf ota_server_simple_20250709_100449.tar.gz
cd ota_server_simple_20250709_100449
```

### 2. 运行安装脚本
```bash
bash install.sh
```

安装脚本会：
- 检查Python环境
- 创建虚拟环境
- 安装依赖包
- 创建必要目录
- 配置环境变量
- 创建启动脚本

### 3. 启动服务
```bash
./run.sh
```

或者后台运行：
```bash
nohup ./run.sh > ota.log 2>&1 &
```

## 📁 目录结构

```
ota_server_simple_20250709_100449/
├── app.py              # 主程序
├── start.py            # 启动脚本
├── requirements.txt    # Python依赖
├── install.sh          # 安装脚本
├── run.sh             # 启动脚本（安装后生成）
├── .env               # 环境配置（安装后生成）
├── venv/              # Python虚拟环境（安装后生成）
├── templates/         # 网页模板
├── static/ota/        # 固件文件存储目录
├── instance/          # 数据库文件目录
├── logs/              # 日志文件目录
└── 使用说明.md         # 详细使用说明
```

## ⚙️ 配置说明

安装时会提示输入：
- **域名或IP**: 服务器的访问地址
- **管理员用户名**: 默认 admin
- **管理员密码**: 默认 123456

配置文件 `.env` 示例：
```bash
SECRET_KEY=auto-generated-key
FLASK_ENV=production
DATABASE_URL=sqlite:///instance/ota_config.db
UPLOAD_FOLDER=/path/to/ota_server/static/ota
DOWNLOAD_BASE_URL=http://your-domain.com:8000/ota/
PORT=8000
ADMIN_USERNAME=admin
ADMIN_PASSWORD=123456
LOG_LEVEL=INFO
LOG_FILE=/path/to/ota_server/logs/ota_server.log
```

## 🌐 访问服务

- **管理界面**: http://your-domain:8000
- **API接口**: http://your-domain:8000/api/update
- **模拟测试**: http://your-domain:8000/simulate

## 📱 API使用示例

### 检查特定类型升级包
```bash
curl -X POST http://your-domain:8000/api/update \
  -d "device=T507" \
  -d "firmware=v1.1" \
  -d "type=incremental"
```

### 获取所有可用升级包
```bash
curl -X POST http://your-domain:8000/api/packages \
  -d "device=T507" \
  -d "firmware=v1.1"
```

## 🔧 服务管理

### 直接运行
```bash
./run.sh                    # 前台运行
nohup ./run.sh &            # 后台运行
```

### 使用systemd（如果以root安装）
```bash
sudo systemctl start ota-server    # 启动
sudo systemctl stop ota-server     # 停止
sudo systemctl restart ota-server  # 重启
sudo systemctl status ota-server   # 状态
```

### 查看日志
```bash
tail -f logs/ota_server.log        # 应用日志
tail -f ota.log                    # 运行日志（如果后台运行）
```

## 🔍 故障排除

### 端口被占用
```bash
# 查看端口占用
netstat -tlnp | grep :8000
# 或者修改 .env 文件中的 PORT=8001
```

### 权限问题
```bash
# 确保目录权限正确
chmod -R 755 .
chmod 600 .env
```

### Python依赖问题
```bash
# 重新安装依赖
source venv/bin/activate
pip install -r requirements.txt
```

## 📋 使用流程

1. **启动服务** → 访问管理界面
2. **添加设备** → 创建设备记录
3. **配置升级路径** → 设置版本升级关系（如 v1.1 → v1.2）
4. **上传升级包** → 上传三种类型的升级包文件
5. **测试功能** → 使用模拟测试验证
6. **客户端集成** → 使用API接口

## 🎯 升级包类型说明

- **full**: 全量升级包 - 完整的固件镜像
- **incremental**: 增量升级包 - 基于差分的升级包，体积小
- **targetfiles**: 目标文件包 - 用于构建的目标文件

## 📞 技术支持

如遇问题：
1. 检查日志文件
2. 确认配置正确
3. 验证网络连接
4. 查看使用说明.md

---

**简单部署版本** - 无需额外用户，直接运行  
**版本**: 20250709_100449  
**包大小**: 32KB
