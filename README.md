# OTA 服务器

一个简单、安全、高效的 OTA (Over-The-Air) 固件更新服务器，支持多设备、多版本的固件管理和分发。

## 功能特性

- 🚀 **简单轻量**: 最小化依赖，专注核心功能
- 📱 **设备管理**: 支持设备的增删改查操作
- 📁 **文件管理**: 固件文件上传、存储和下载
- 📊 **下载统计**: 记录设备下载次数和历史
- 🔍 **模拟测试**: 内置API测试工具
- 🌐 **RESTful API**: 标准的HTTP API接口

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 启动服务器

```bash
python start.py
```

或者使用gunicorn（推荐生产环境）：

```bash
gunicorn -w 4 -b 127.0.0.1:8000 app:app
```

### 3. 访问管理界面

打开浏览器访问 `http://localhost:8000`

默认管理员账号：
- 用户名: admin
- 密码: 123456

## 部署配置

### Nginx配置示例

```nginx
server {
    listen 80;
    server_name ota.tyw.com;

    # 增加文件上传大小限制
    client_max_body_size 1G;

    # Flask 应用代理
    location / {
        proxy_pass         http://127.0.0.1:8000;
        proxy_set_header   Host $host;
        proxy_set_header   X-Real-IP $remote_addr;
        proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Proto $scheme;

        # 增加代理超时时间（用于大文件上传）
        proxy_read_timeout 300;
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
    }

    # 固件文件直接托管
    location /ota/ {
        alias /home/<USER>/ota_server/static/ota/;
        autoindex on;
    }
}
```

### Systemd服务配置

创建 `/etc/systemd/system/ota-server.service`:

```ini
[Unit]
Description=OTA Flask Application (gunicorn)
After=network.target

[Service]
User=tyw
Group=tyw
WorkingDirectory=/home/<USER>/ota_server
Environment="PATH=/home/<USER>/ota_server/venv/bin"
ExecStart=/home/<USER>/ota_server/venv/bin/gunicorn -w 4 -b 127.0.0.1:8000 app:app
Restart=always

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
sudo systemctl enable ota-server
sudo systemctl start ota-server
```

## API接口

### 检查更新

**请求:**
```
POST /api/update
Content-Type: application/x-www-form-urlencoded

device=your_device_name&firmware=current_version
```

**响应:**
```json
{
  "update": true,
  "latest_version": "v2.1.0",
  "url": "http://server/ota/firmware.bin",
  "file_size": 1048576,
  "md5": "d41d8cd98f00b204e9800998ecf8427e"
}
```

### 下载固件

```
GET /ota/firmware_filename.bin
```

## 环境变量

- `DOWNLOAD_BASE_URL`: 固件下载基础URL（默认: http://ota.tyw.com/ota/）
- `DATABASE_URL`: 数据库连接URL（默认: sqlite:///ota_config.db）
- `SECRET_KEY`: Flask密钥（默认: change-this-secret-key）
- `ADMIN_USERNAME`: 管理员用户名（默认: admin）
- `ADMIN_PASSWORD`: 管理员密码（默认: 123456）
- `PORT`: 服务端口（默认: 8000）

## 目录结构

```
ota_server/
├── app.py              # 主应用文件
├── start.py            # 启动脚本
├── requirements.txt    # 依赖包
├── static/ota/         # 固件文件存储目录
├── templates/          # HTML模板
│   ├── base.html
│   ├── login.html
│   ├── device_list.html
│   ├── device_form.html
│   └── simulate.html
└── ota_config.db       # SQLite数据库文件
```

## 注意事项

1. 生产环境请修改默认的管理员密码
2. 确保 `static/ota/` 目录有写入权限
3. 建议使用nginx托管静态文件以提高性能
4. 定期备份数据库文件

## 许可证

MIT License
