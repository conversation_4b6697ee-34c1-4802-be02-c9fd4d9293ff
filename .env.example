# Flask配置
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# 数据库配置
DATABASE_URL=sqlite:///ota_config.db

# 文件上传配置
UPLOAD_FOLDER=static/ota
MAX_CONTENT_LENGTH=524288000  # 500MB in bytes

# OTA服务配置
DOWNLOAD_BASE_URL=http://your-server.com/static/ota/
OTA_API_KEY=your-api-key-here

# 安全配置
SESSION_COOKIE_SECURE=false  # 生产环境设为true
WTF_CSRF_TIME_LIMIT=3600

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=ota_server.log

# 限流配置
RATELIMIT_STORAGE_URL=memory://

# 管理员账号配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password-here
