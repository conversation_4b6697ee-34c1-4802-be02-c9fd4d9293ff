#!/bin/bash

# OTA服务器简单安装脚本
# 基于当前本机运行方式

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
    log_error "此脚本需要root权限运行"
    log_info "请使用: sudo bash simple_install.sh"
    exit 1
fi

log_info "开始安装OTA服务器..."

# 1. 安装系统依赖
log_info "安装系统依赖..."
if command -v apt &> /dev/null; then
    apt update
    apt install -y python3 python3-pip python3-venv nginx
elif command -v yum &> /dev/null; then
    yum install -y python3 python3-pip python3-venv nginx
elif command -v dnf &> /dev/null; then
    dnf install -y python3 python3-pip python3-venv nginx
else
    log_error "不支持的操作系统"
    exit 1
fi

# 2. 创建用户和目录
log_info "创建用户和目录..."
useradd -r -s /bin/bash -d /opt/ota_server ota 2>/dev/null || true
mkdir -p /opt/ota_server
cp -r * /opt/ota_server/ 2>/dev/null || true
chown -R ota:ota /opt/ota_server

# 3. 创建Python虚拟环境
log_info "创建Python虚拟环境..."
cd /opt/ota_server
sudo -u ota python3 -m venv venv
sudo -u ota venv/bin/pip install --upgrade pip
sudo -u ota venv/bin/pip install -r requirements.txt

# 4. 获取配置信息
read -p "请输入域名或IP地址: " DOMAIN
read -p "请输入管理员用户名 [admin]: " ADMIN_USER
ADMIN_USER=${ADMIN_USER:-admin}

# 生成随机密码
ADMIN_PASSWORD=$(openssl rand -base64 12 2>/dev/null || echo "123456")
SECRET_KEY=$(openssl rand -hex 32 2>/dev/null || echo "change-this-secret-key")

# 5. 创建环境配置
log_info "创建环境配置..."
cat > /opt/ota_server/.env << EOF
SECRET_KEY=$SECRET_KEY
FLASK_ENV=production
DATABASE_URL=sqlite:///instance/ota_config.db
UPLOAD_FOLDER=/opt/ota_server/static/ota
DOWNLOAD_BASE_URL=http://$DOMAIN/ota/
PORT=8000
ADMIN_USERNAME=$ADMIN_USER
ADMIN_PASSWORD=$ADMIN_PASSWORD
LOG_LEVEL=INFO
LOG_FILE=/opt/ota_server/logs/ota_server.log
EOF

chown ota:ota /opt/ota_server/.env
chmod 600 /opt/ota_server/.env

# 6. 创建必要目录
sudo -u ota mkdir -p static/ota instance logs

# 7. 配置systemd服务
log_info "配置systemd服务..."
cat > /etc/systemd/system/ota-server.service << 'EOF'
[Unit]
Description=OTA Server
After=network.target

[Service]
Type=simple
User=ota
Group=ota
WorkingDirectory=/opt/ota_server
Environment=PATH=/opt/ota_server/venv/bin
EnvironmentFile=/opt/ota_server/.env
ExecStart=/opt/ota_server/venv/bin/python /opt/ota_server/start.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable ota-server

# 8. 配置nginx
log_info "配置nginx..."
cat > /etc/nginx/sites-available/ota-server << EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    client_max_body_size 1G;
    
    location /ota/ {
        alias /opt/ota_server/static/ota/;
        expires 1d;
    }
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

# 启用nginx配置
ln -sf /etc/nginx/sites-available/ota-server /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default
nginx -t
systemctl enable nginx
systemctl restart nginx

# 9. 启动服务
log_info "启动服务..."
systemctl start ota-server

# 10. 检查服务状态
sleep 3
if systemctl is-active --quiet ota-server; then
    log_info "OTA服务启动成功"
else
    log_error "OTA服务启动失败"
    systemctl status ota-server
    exit 1
fi

# 11. 显示安装信息
log_info "安装完成！"
echo
echo "=================================="
echo "OTA服务器安装信息"
echo "=================================="
echo "访问地址: http://$DOMAIN"
echo "管理员用户: $ADMIN_USER"
echo "管理员密码: $ADMIN_PASSWORD"
echo
echo "服务管理命令:"
echo "  启动: sudo systemctl start ota-server"
echo "  停止: sudo systemctl stop ota-server"
echo "  重启: sudo systemctl restart ota-server"
echo "  状态: sudo systemctl status ota-server"
echo "  日志: sudo journalctl -u ota-server -f"
echo
echo "文件位置:"
echo "  程序目录: /opt/ota_server"
echo "  配置文件: /opt/ota_server/.env"
echo "  数据库: /opt/ota_server/instance/ota_config.db"
echo "  固件存储: /opt/ota_server/static/ota"
echo "=================================="
