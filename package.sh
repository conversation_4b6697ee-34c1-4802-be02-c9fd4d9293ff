#!/bin/bash

# OTA服务器打包脚本
# 生成纯净的部署包

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 版本信息
VERSION=$(date +%Y%m%d_%H%M%S)
PACKAGE_NAME="ota_server_${VERSION}"
PACKAGE_DIR="dist/${PACKAGE_NAME}"

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 创建打包目录
create_package_dir() {
    log_step "创建打包目录..."
    
    rm -rf dist
    mkdir -p "$PACKAGE_DIR"
    
    log_info "打包目录: $PACKAGE_DIR"
}

# 复制源码文件
copy_source_files() {
    log_step "复制源码文件..."
    
    # 主要源码文件
    cp app.py "$PACKAGE_DIR/"
    cp start.py "$PACKAGE_DIR/"
    cp start_production.py "$PACKAGE_DIR/"
    cp gunicorn_config.py "$PACKAGE_DIR/"
    cp requirements.txt "$PACKAGE_DIR/"
    
    # 模板文件
    cp -r templates "$PACKAGE_DIR/"
    
    # 静态文件目录结构
    mkdir -p "$PACKAGE_DIR/static/ota"
    
    # 实例目录
    mkdir -p "$PACKAGE_DIR/instance"
    
    # 日志目录
    mkdir -p "$PACKAGE_DIR/logs"
    
    log_info "源码文件复制完成"
}

# 复制文档和配置文件
copy_docs_and_configs() {
    log_step "复制文档和配置文件..."
    
    # 文档文件
    cp README.md "$PACKAGE_DIR/" 2>/dev/null || echo "# OTA Server" > "$PACKAGE_DIR/README.md"
    cp INSTALL.md "$PACKAGE_DIR/"
    cp 使用说明.md "$PACKAGE_DIR/" 2>/dev/null || true
    
    # 部署脚本
    cp deploy.sh "$PACKAGE_DIR/"
    chmod +x "$PACKAGE_DIR/deploy.sh"
    
    # Nginx配置示例
    cp nginx.conf.example "$PACKAGE_DIR/" 2>/dev/null || create_nginx_example
    
    log_info "文档和配置文件复制完成"
}

# 创建nginx配置示例
create_nginx_example() {
    cat > "$PACKAGE_DIR/nginx.conf.example" << 'EOF'
server {
    listen 80;
    server_name your-domain.com;
    
    client_max_body_size 1G;
    
    location /ota/ {
        alias /opt/ota_server/static/ota/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF
}

# 创建环境配置模板
create_env_template() {
    log_step "创建环境配置模板..."
    
    cat > "$PACKAGE_DIR/.env.example" << 'EOF'
# 基础配置
SECRET_KEY=change-this-secret-key
FLASK_ENV=production

# 数据库配置
DATABASE_URL=sqlite:///instance/ota_config.db

# 文件配置
UPLOAD_FOLDER=/opt/ota_server/static/ota
MAX_FILE_SIZE=536870912
MAX_CONTENT_LENGTH=1073741824

# 网络配置
DOWNLOAD_BASE_URL=https://your-domain.com/ota/
PORT=8000

# 管理员配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=change-this-password

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/opt/ota_server/logs/ota_server.log
EOF

    log_info "环境配置模板创建完成"
}

# 创建systemd服务文件模板
create_systemd_template() {
    log_step "创建systemd服务文件模板..."
    
    cat > "$PACKAGE_DIR/ota-server.service" << 'EOF'
[Unit]
Description=OTA Server
After=network.target

[Service]
Type=simple
User=ota
Group=ota
WorkingDirectory=/opt/ota_server
Environment=PATH=/opt/ota_server/venv/bin
EnvironmentFile=/opt/ota_server/.env
ExecStart=/opt/ota_server/venv/bin/python /opt/ota_server/start_production.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=3
StandardOutput=journal
StandardError=journal

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/opt/ota_server
ProtectHome=true

[Install]
WantedBy=multi-user.target
EOF

    log_info "systemd服务文件模板创建完成"
}

# 创建快速安装脚本
create_quick_install() {
    log_step "创建快速安装脚本..."
    
    cat > "$PACKAGE_DIR/quick_install.sh" << 'EOF'
#!/bin/bash

# OTA服务器快速安装脚本

set -e

echo "OTA服务器快速安装"
echo "=================="

# 检查root权限
if [[ $EUID -ne 0 ]]; then
    echo "错误: 需要root权限"
    echo "请使用: sudo bash quick_install.sh"
    exit 1
fi

# 获取用户输入
read -p "请输入域名: " DOMAIN
read -p "请输入管理员用户名 [admin]: " ADMIN_USER
ADMIN_USER=${ADMIN_USER:-admin}

# 生成随机密码
ADMIN_PASSWORD=$(openssl rand -base64 12)
SECRET_KEY=$(openssl rand -hex 32)

echo "正在安装..."

# 安装依赖
if command -v apt &> /dev/null; then
    apt update
    apt install -y python3 python3-pip python3-venv nginx
elif command -v yum &> /dev/null; then
    yum install -y python3 python3-pip python3-venv nginx
fi

# 创建用户
useradd -r -s /bin/bash -d /opt/ota_server ota 2>/dev/null || true
mkdir -p /opt/ota_server
cp -r * /opt/ota_server/
chown -R ota:ota /opt/ota_server

# 创建虚拟环境
cd /opt/ota_server
sudo -u ota python3 -m venv venv
sudo -u ota venv/bin/pip install -r requirements.txt

# 配置环境变量
cat > .env << EOL
SECRET_KEY=$SECRET_KEY
FLASK_ENV=production
DATABASE_URL=sqlite:///instance/ota_config.db
UPLOAD_FOLDER=/opt/ota_server/static/ota
DOWNLOAD_BASE_URL=https://$DOMAIN/ota/
PORT=8000
ADMIN_USERNAME=$ADMIN_USER
ADMIN_PASSWORD=$ADMIN_PASSWORD
LOG_LEVEL=INFO
LOG_FILE=/opt/ota_server/logs/ota_server.log
EOL

chown ota:ota .env
chmod 600 .env

# 初始化数据库
sudo -u ota bash -c "source venv/bin/activate && python start.py" &
sleep 5
pkill -f start.py || true

# 配置systemd
cp ota-server.service /etc/systemd/system/
systemctl daemon-reload
systemctl enable ota-server

# 配置nginx
sed "s/your-domain.com/$DOMAIN/g" nginx.conf.example > /etc/nginx/sites-available/ota-server
ln -sf /etc/nginx/sites-available/ota-server /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default
nginx -t
systemctl enable nginx

# 启动服务
systemctl start ota-server
systemctl start nginx

echo "安装完成！"
echo "访问地址: http://$DOMAIN"
echo "管理员用户: $ADMIN_USER"
echo "管理员密码: $ADMIN_PASSWORD"
EOF

    chmod +x "$PACKAGE_DIR/quick_install.sh"
    log_info "快速安装脚本创建完成"
}

# 创建版本信息文件
create_version_info() {
    log_step "创建版本信息文件..."
    
    cat > "$PACKAGE_DIR/VERSION" << EOF
OTA Server
版本: $VERSION
打包时间: $(date)
Python要求: 3.8+
EOF

    log_info "版本信息文件创建完成"
}

# 清理临时文件
cleanup_temp_files() {
    log_step "清理临时文件..."
    
    # 删除Python缓存
    find "$PACKAGE_DIR" -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    find "$PACKAGE_DIR" -name "*.pyc" -delete 2>/dev/null || true
    find "$PACKAGE_DIR" -name "*.pyo" -delete 2>/dev/null || true
    
    # 删除开发文件
    rm -f "$PACKAGE_DIR/.env" 2>/dev/null || true
    rm -rf "$PACKAGE_DIR/venv" 2>/dev/null || true
    rm -rf "$PACKAGE_DIR/instance/ota_config.db" 2>/dev/null || true
    
    log_info "临时文件清理完成"
}

# 创建压缩包
create_archive() {
    log_step "创建压缩包..."
    
    cd dist
    tar -czf "${PACKAGE_NAME}.tar.gz" "$PACKAGE_NAME"
    zip -r "${PACKAGE_NAME}.zip" "$PACKAGE_NAME" >/dev/null
    
    # 计算文件大小和校验和
    TAR_SIZE=$(du -h "${PACKAGE_NAME}.tar.gz" | cut -f1)
    ZIP_SIZE=$(du -h "${PACKAGE_NAME}.zip" | cut -f1)
    TAR_MD5=$(md5sum "${PACKAGE_NAME}.tar.gz" | cut -d' ' -f1)
    ZIP_MD5=$(md5sum "${PACKAGE_NAME}.zip" | cut -d' ' -f1)
    
    log_info "压缩包创建完成"
    echo "  tar.gz: ${TAR_SIZE} (MD5: ${TAR_MD5})"
    echo "  zip: ${ZIP_SIZE} (MD5: ${ZIP_MD5})"
}

# 显示打包信息
show_package_info() {
    log_step "打包完成！"
    
    echo
    echo "=================================="
    echo "OTA服务器部署包信息"
    echo "=================================="
    echo "版本: $VERSION"
    echo "包名: $PACKAGE_NAME"
    echo
    echo "文件列表:"
    echo "  dist/${PACKAGE_NAME}.tar.gz"
    echo "  dist/${PACKAGE_NAME}.zip"
    echo "  dist/${PACKAGE_NAME}/ (源码目录)"
    echo
    echo "安装方法:"
    echo "1. 解压部署包到目标服务器"
    echo "2. 运行快速安装: sudo bash quick_install.sh"
    echo "3. 或者运行完整部署: sudo bash deploy.sh"
    echo
    echo "包含文件:"
    ls -la "$PACKAGE_DIR"
    echo "=================================="
}

# 主函数
main() {
    log_info "开始打包OTA服务器..."
    
    create_package_dir
    copy_source_files
    copy_docs_and_configs
    create_env_template
    create_systemd_template
    create_quick_install
    create_version_info
    cleanup_temp_files
    create_archive
    show_package_info
    
    log_info "打包完成！"
}

# 运行主函数
main "$@"
