# OTA服务器部署总结

## 📦 部署包内容

本项目提供了完整的OTA服务器部署解决方案，包含以下文件：

### 核心文件
- `app.py` - 主应用程序，包含所有业务逻辑
- `start.py` - 开发环境启动脚本
- `start_production.py` - 生产环境启动脚本
- `gunicorn_config.py` - Gunicorn生产服务器配置
- `requirements.txt` - Python依赖包列表

### 模板和静态文件
- `templates/` - HTML模板目录
  - 管理界面模板
  - 设备管理页面
  - 升级包管理页面
  - 模拟测试页面
- `static/ota/` - 固件文件存储目录

### 配置文件
- `.env.example` - 环境变量配置模板
- `nginx.conf.example` - Nginx配置示例
- `ota-server.service` - Systemd服务配置

### 部署脚本
- `deploy.sh` - 完整自动部署脚本
- `quick_install.sh` - 快速安装脚本
- `package.sh` - 打包脚本

### 文档
- `README.md` - 项目说明文档
- `INSTALL.md` - 详细安装部署教程
- `使用说明.md` - 使用说明文档
- `VERSION` - 版本信息

## 🚀 三种部署方式

### 1. 快速部署（推荐新手）

```bash
# 解压部署包
tar -xzf ota_server_*.tar.gz
cd ota_server_*

# 运行快速安装脚本
sudo bash quick_install.sh
```

**特点**：
- 一键安装，自动配置
- 适合快速体验和测试
- 自动生成安全密码
- 基本的生产环境配置

### 2. 完整部署（推荐生产环境）

```bash
# 解压部署包
tar -xzf ota_server_*.tar.gz
cd ota_server_*

# 运行完整部署脚本
sudo bash deploy.sh
```

**特点**：
- 完整的生产环境配置
- 包含安全配置和优化
- 支持SSL证书配置
- 完整的日志和监控配置

### 3. 手动部署（推荐高级用户）

参考 `INSTALL.md` 文档进行手动部署

**特点**：
- 完全可控的部署过程
- 可以根据需求定制配置
- 适合复杂的部署环境

## 🔧 支持的升级包类型

系统支持三种升级包类型，符合您的需求：

1. **full** - 全量升级包
   - 完整的固件镜像
   - 适用于大版本升级

2. **incremental** - 增量升级包
   - 基于差分的升级包
   - 文件体积小，升级速度快

3. **targetfiles** - 目标文件包
   - 用于构建的目标文件
   - 支持自定义构建流程

## 📁 目录结构

部署后的目录结构：

```
/opt/ota_server/
├── app.py                    # 主应用
├── start_production.py       # 生产启动脚本
├── gunicorn_config.py        # Gunicorn配置
├── .env                      # 环境变量配置
├── venv/                     # Python虚拟环境
├── static/ota/               # 固件文件存储
│   └── device_name/
│       └── version_to_version/
│           ├── full_*.zip
│           ├── incremental_*.zip
│           └── targetfiles_*.zip
├── instance/                 # 数据库文件
├── logs/                     # 日志文件
└── templates/                # 模板文件
```

## 🌐 网络架构

```
Internet
    ↓
[Nginx] (Port 80/443)
    ↓
[Gunicorn] (Port 8000)
    ↓
[Flask App]
    ↓
[SQLite Database]
```

## 🔐 安全特性

- **文件安全**：文件类型验证、大小限制、路径遍历防护
- **数据安全**：SQL注入防护、XSS防护、CSRF保护
- **传输安全**：HTTPS支持、安全HTTP头
- **访问控制**：用户认证、权限管理
- **完整性校验**：MD5/SHA256文件校验

## 📊 监控和维护

### 服务管理
```bash
# 查看服务状态
sudo systemctl status ota-server

# 启动/停止/重启服务
sudo systemctl start/stop/restart ota-server

# 查看日志
sudo journalctl -u ota-server -f
tail -f /opt/ota_server/logs/ota_server.log
```

### 健康检查
- 访问 `/api/health` 检查服务健康状态
- 访问 `/api/stats` 查看统计信息

### 备份
- 数据库文件：`/opt/ota_server/instance/ota_config.db`
- 固件文件：`/opt/ota_server/static/ota/`
- 配置文件：`/opt/ota_server/.env`

## 🔧 常用配置

### 修改上传限制
编辑 `.env` 文件：
```bash
MAX_FILE_SIZE=**********  # 1GB
MAX_CONTENT_LENGTH=**********  # 2GB
```

### 修改下载URL
编辑 `.env` 文件：
```bash
DOWNLOAD_BASE_URL=https://your-domain.com/ota/
```

### 启用HTTPS
1. 获取SSL证书（推荐Let's Encrypt）
2. 修改Nginx配置启用SSL
3. 更新 `DOWNLOAD_BASE_URL` 为https

## 📱 客户端集成示例

### 检查更新
```bash
curl -X POST https://your-domain.com/api/update \
  -d "device=T507" \
  -d "firmware=v1.1" \
  -d "type=incremental"
```

### 获取所有升级包
```bash
curl -X POST https://your-domain.com/api/packages \
  -d "device=T507" \
  -d "firmware=v1.1"
```

## 🎯 使用流程

1. **部署服务器**：使用提供的部署脚本
2. **登录管理界面**：使用默认或配置的管理员账号
3. **添加设备**：创建设备记录
4. **配置升级路径**：设置版本升级关系
5. **上传升级包**：上传三种类型的升级包
6. **客户端集成**：使用API接口检查和下载更新
7. **监控维护**：查看下载统计和系统状态

## 🆘 故障排除

### 常见问题
1. **服务无法启动**：检查端口占用、权限问题
2. **文件上传失败**：检查磁盘空间、文件大小限制
3. **下载失败**：检查文件路径、Nginx配置
4. **数据库错误**：检查文件权限、磁盘空间

### 日志位置
- 应用日志：`/opt/ota_server/logs/ota_server.log`
- Gunicorn日志：`/opt/ota_server/logs/gunicorn_*.log`
- Nginx日志：`/var/log/nginx/`
- 系统日志：`journalctl -u ota-server`

## 📞 技术支持

如遇到问题，请：
1. 查看相关日志文件
2. 检查配置文件
3. 参考文档说明
4. 提交Issue反馈

---

**部署包版本**：20250709_084257  
**更新时间**：2025年7月9日  
**支持的Python版本**：3.8+  
**推荐的操作系统**：Ubuntu 20.04+, CentOS 8+, Debian 11+
